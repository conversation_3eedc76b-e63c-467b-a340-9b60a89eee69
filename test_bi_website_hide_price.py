#!/usr/bin/env python3
"""
Test script for bi_website_hide_price module
"""

import os
import sys

def test_module_structure():
    """Test that all required files exist"""
    module_path = "bi_website_hide_price"
    
    required_files = [
        "__manifest__.py",
        "__init__.py",
        "models/__init__.py",
        "models/product_template.py", 
        "models/res_config_settings.py",
        "views/product_template_views.xml",
        "views/website_config_settings_views.xml",
        "views/website_templates.xml",
        "README.md"
    ]
    
    print("Testing module structure...")
    
    for file_path in required_files:
        full_path = os.path.join(module_path, file_path)
        if os.path.exists(full_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MISSING")
            return False
    
    print("\n✓ All required files present!")
    return True

def test_manifest_syntax():
    """Test that manifest file has valid syntax"""
    print("\nTesting manifest syntax...")
    
    try:
        with open("bi_website_hide_price/__manifest__.py", 'r') as f:
            content = f.read()
        
        # Basic syntax check
        compile(content, "__manifest__.py", "exec")
        print("✓ Manifest syntax is valid")
        return True
    except SyntaxError as e:
        print(f"✗ Manifest syntax error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading manifest: {e}")
        return False

def test_python_syntax():
    """Test that Python files have valid syntax"""
    print("\nTesting Python file syntax...")
    
    python_files = [
        "bi_website_hide_price/models/product_template.py",
        "bi_website_hide_price/models/res_config_settings.py"
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            compile(content, file_path, "exec")
            print(f"✓ {file_path}")
        except SyntaxError as e:
            print(f"✗ {file_path} - Syntax error: {e}")
            return False
        except Exception as e:
            print(f"✗ {file_path} - Error: {e}")
            return False
    
    print("✓ All Python files have valid syntax")
    return True

def main():
    """Run all tests"""
    print("=" * 50)
    print("Testing bi_website_hide_price module")
    print("=" * 50)
    
    tests = [
        test_module_structure,
        test_manifest_syntax,
        test_python_syntax
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ ALL TESTS PASSED!")
        print("The module is ready for installation.")
    else:
        print("✗ SOME TESTS FAILED!")
        print("Please fix the issues before installing.")
    print("=" * 50)
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
