#!/usr/bin/env python3
"""
Reset and test the bi_website_hide_price module with clean templates
"""

import subprocess
import time
import os

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description} completed")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} error: {e}")
        return False

def check_template_content():
    """Verify the template contains clean code"""
    print("\n🔍 Checking template content...")
    
    try:
        with open('bi_website_hide_price/views/website_templates.xml', 'r') as f:
            content = f.read()
        
        # Check for problematic code
        if 'FRESH START' in content:
            print("❌ Old JavaScript code still present!")
            return False
        
        if 'hideAllMicrophonePrices' in content:
            print("❌ Aggressive JavaScript still present!")
            return False
        
        if 'el.remove()' in content:
            print("❌ DOM manipulation code still present!")
            return False
        
        # Check for clean template code
        if 't-if="not product.website_hide_price"' in content:
            print("✅ Clean template conditions found")
        else:
            print("⚠️  Template conditions not found")
        
        if 'product_price_hide' in content:
            print("✅ Clean template ID found")
        else:
            print("⚠️  Template ID not found")
        
        print("✅ Template content is clean")
        return True
        
    except Exception as e:
        print(f"❌ Error checking template: {e}")
        return False

def wait_for_odoo():
    """Wait for Odoo to be ready"""
    print("\n⏳ Waiting for Odoo to be ready...")
    
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            result = subprocess.run([
                'curl', '-s', '-o', '/dev/null', '-w', '%{http_code}', 
                'http://localhost:8069'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0 and result.stdout.strip() == '200':
                print("✅ Odoo is ready!")
                return True
                
        except:
            pass
        
        print(f"⏳ Attempt {attempt + 1}/{max_attempts}...")
        time.sleep(2)
    
    print("❌ Odoo not ready after waiting")
    return False

def display_testing_instructions():
    """Display instructions for testing"""
    print("\n" + "="*60)
    print("🎯 CLEAN MODULE TESTING INSTRUCTIONS")
    print("="*60)
    
    print("\n✅ WHAT WAS FIXED:")
    print("   • Removed aggressive JavaScript code")
    print("   • Replaced with clean Odoo QWeb templates")
    print("   • No more DOM manipulation or cache issues")
    print("   • Proper template inheritance")
    
    print("\n🚀 TESTING STEPS:")
    print("   1. Open http://localhost:8069")
    print("   2. Create a NEW database (important!)")
    print("   3. Install required modules:")
    print("      - Website")
    print("      - eCommerce") 
    print("      - bi_website_hide_price")
    print("   4. Create test products")
    print("   5. Enable price hiding on products")
    print("   6. Test the functionality")
    
    print("\n🎯 EXPECTED BEHAVIOR:")
    print("   • No JavaScript console errors")
    print("   • Clean price hiding using templates")
    print("   • Custom messages instead of prices")
    print("   • Contact Us buttons instead of Add to Cart")
    
    print("\n⚠️  IMPORTANT:")
    print("   • Use a FRESH database - old databases may have cached templates")
    print("   • Clear browser cache if needed")
    print("   • Check browser console for any remaining errors")

def main():
    print("🚀 RESETTING AND TESTING BI_WEBSITE_HIDE_PRICE MODULE")
    print("="*60)
    
    # Check if Odoo is running
    print("🔍 Checking Odoo status...")
    result = subprocess.run(['docker-compose', 'ps'], capture_output=True, text=True)
    if 'Up' not in result.stdout:
        print("❌ Odoo containers not running. Please start them first:")
        print("   docker-compose up -d")
        return
    
    print("✅ Odoo containers are running")
    
    # Check template content
    if not check_template_content():
        print("\n❌ Template issues found. Please fix before testing.")
        return
    
    # Wait for Odoo to be ready
    if not wait_for_odoo():
        print("\n❌ Odoo not accessible. Please check the service.")
        return
    
    # Display testing instructions
    display_testing_instructions()
    
    print("\n" + "="*60)
    print("🎉 MODULE RESET COMPLETE!")
    print("Ready for clean testing with proper templates!")
    print("="*60)

if __name__ == "__main__":
    main()
