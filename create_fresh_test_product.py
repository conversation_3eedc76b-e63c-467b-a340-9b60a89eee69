#!/usr/bin/env python3
"""
Create a test microphone product on fresh Odoo database
"""

import xmlrpc.client

# Odoo connection details
url = 'http://localhost:8069'
db = 'fresh_odoo18'
username = 'admin'
password = 'admin'

try:
    # Connect to Odoo
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})
    
    if not uid:
        print("❌ Authentication failed!")
        exit(1)
    
    print(f"✅ Connected to Odoo as user ID: {uid}")
    
    # Get models proxy
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    
    # Create microphone product
    product_data = {
        'name': 'Test Microphone',
        'list_price': 1.00,
        'type': 'consu',
        'is_published': True,
        'website_hide_price': True,  # This is our custom field
        'website_hide_price_message': 'Contact us for pricing',
        'description_sale': 'Professional microphone for testing price hiding functionality.',
        'categ_id': 1,  # All category
    }
    
    # Create the product
    product_id = models.execute_kw(
        db, uid, password,
        'product.template', 'create',
        [product_data]
    )
    
    print(f"✅ Created microphone product with ID: {product_id}")
    print(f"🔗 Product URL: http://localhost:8069/shop/test-microphone-{product_id}")
    
    # Also create a normal product for comparison
    normal_product_data = {
        'name': 'Normal Product',
        'list_price': 99.99,
        'type': 'consu',
        'is_published': True,
        'website_hide_price': False,  # Normal product shows price
        'description_sale': 'Normal product that shows price for comparison.',
        'categ_id': 1,
    }
    
    normal_product_id = models.execute_kw(
        db, uid, password,
        'product.template', 'create',
        [normal_product_data]
    )
    
    print(f"✅ Created normal product with ID: {normal_product_id}")
    print(f"🔗 Normal Product URL: http://localhost:8069/shop/normal-product-{normal_product_id}")
    
    print("\n🎯 TEST INSTRUCTIONS:")
    print("1. Visit shop page: http://localhost:8069/shop")
    print("2. Look for 'Test Microphone' - should show NO price")
    print("3. Look for 'Normal Product' - should show $99.99")
    print("4. Click on microphone product - should show clean catalog page")
    print("5. Check browser console (F12) for price hiding messages")
    
except Exception as e:
    print(f"❌ Error: {e}")
