# Website Product Extra Information

A custom Odoo 18 module that allows you to add structured extra information to products that will be displayed in a clean table format on the website product detail pages.

## Features

- ✅ Add unlimited title-content pairs per product
- ✅ HTML content support for rich formatting
- ✅ Clean, responsive table layout on website
- ✅ Perfect for technical specifications, features, care instructions, etc.
- ✅ Fully compatible with Odoo 18 Community Edition
- ✅ Professional styling with responsive design
- ✅ Easy-to-use backend interface

## Installation

1. Copy this module to your Odoo addons directory
2. Restart your Odoo server
3. Go to Apps and update the apps list
4. Search for "Website Product Extra Information" and install it

### Docker Installation

If using Docker, place the module in your custom addons directory and run:

```bash
docker exec odoo-container-name odoo -d your-database --stop-after-init -i website_product_extra_info --addons-path=/mnt/extra-addons,/usr/lib/python3/dist-packages/odoo/addons
```

## Usage

### Adding Extra Information to Products

1. Go to **Sales > Products** and open any product
2. Navigate to the **"Extra Information"** tab
3. Click **"Add a line"** to add new information
4. Fill in:
   - **Title**: The label for this information (e.g., "Specifications", "Features")
   - **Content**: The detailed information (supports HTML formatting)
5. Use the handle (drag icon) to reorder the information
6. Save the product

### Viewing on Website

1. Go to your website and navigate to any product page
2. If the product has extra information, you'll see an "Additional Information" section
3. The information is displayed in a clean, responsive table format

## Perfect for Audio Equipment

This module is ideal for audio equipment and electronics where you need to display:

- **Technical Specifications**: Frequency response, power ratings, dimensions
- **Features**: Key features and capabilities
- **Compatibility**: Compatible devices and formats
- **Care Instructions**: Maintenance and care guidelines
- **Warranty Information**: Warranty terms and conditions

## Example Usage for Audio Products

### Microphone Product
- **Title**: "Technical Specifications"
- **Content**: 
  ```html
  <ul>
    <li>Frequency Response: 20Hz - 20kHz</li>
    <li>Sensitivity: -34dBV/Pa</li>
    <li>Max SPL: 144dB</li>
    <li>Connector: XLR-3M</li>
  </ul>
  ```

### Speaker Product
- **Title**: "Features"
- **Content**:
  ```html
  <ul>
    <li>Bi-amplified design</li>
    <li>Built-in DSP processing</li>
    <li>Multiple input options</li>
    <li>Professional grade components</li>
  </ul>
  ```

## Technical Details

### Models
- `product.extra.info`: Stores the extra information lines
- `product.template`: Extended with extra_info_ids field

### Fields
- `name`: Title of the information
- `content`: HTML content
- `sequence`: Order of display
- `product_tmpl_id`: Link to product template

### Security
- Users can read/write extra information
- Public users can only read (for website display)

## Customization

The module can be easily customized:

- **Styling**: Modify `static/src/css/website_product_extra_info.css`
- **Template**: Modify `views/website_product_extra_info_templates.xml`
- **Backend Views**: Modify `views/product_template_views.xml`

## Support

This is a custom module created for your specific needs. For modifications or support, please contact your developer.

## License

LGPL-3 License
