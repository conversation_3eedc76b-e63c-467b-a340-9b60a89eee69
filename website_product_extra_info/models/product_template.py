# -*- coding: utf-8 -*-
from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    extra_info_ids = fields.One2many(
        'product.extra.info',
        'product_tmpl_id',
        string='Extra Information',
        help='Additional information to display on the website product page'
    )
    
    extra_info_count = fields.Integer(
        string='Extra Info Count',
        compute='_compute_extra_info_count',
        store=True
    )
    
    @api.depends('extra_info_ids')
    def _compute_extra_info_count(self):
        """Compute the number of extra info lines"""
        for product in self:
            product.extra_info_count = len(product.extra_info_ids)
    
    def has_extra_info(self):
        """Check if product has extra information to display"""
        return bool(self.extra_info_ids)
