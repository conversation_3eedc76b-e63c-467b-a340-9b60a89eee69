# -*- coding: utf-8 -*-
from odoo import models, fields, api


class ProductExtraInfo(models.Model):
    _name = 'product.extra.info'
    _description = 'Product Extra Information'
    _order = 'sequence, id'

    name = fields.Char(
        string='Title',
        required=True,
        help='Title for this extra information line'
    )
    
    content = fields.Html(
        string='Content',
        required=True,
        help='HTML content for this extra information'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Sequence order for displaying the information'
    )
    
    product_tmpl_id = fields.Many2one(
        'product.template',
        string='Product Template',
        required=True,
        ondelete='cascade'
    )
    
    @api.model
    def create(self, vals):
        """Override create to ensure proper sequence"""
        if 'sequence' not in vals or not vals['sequence']:
            # Get the next sequence number for this product
            last_sequence = self.search([
                ('product_tmpl_id', '=', vals.get('product_tmpl_id'))
            ], order='sequence desc', limit=1)
            vals['sequence'] = (last_sequence.sequence + 10) if last_sequence else 10
        return super().create(vals)
