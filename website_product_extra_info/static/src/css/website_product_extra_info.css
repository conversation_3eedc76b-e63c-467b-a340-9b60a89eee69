/* Simple Product Extra Info Styles */

.product-extra-info {
    border: 1px solid #ddd;
    background-color: #fff;
    margin: 20px 0;
    padding: 0;
}

.product-extra-info-header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    padding: 10px 15px;
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-extra-info-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.toggle-extra-info {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.toggle-extra-info:hover {
    background-color: #e9e9e9;
}

.product-extra-info-content {
    padding: 15px;
}

.product-specs-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 14px;
}

.product-specs-table tbody tr {
    border-bottom: 1px solid #eee;
}

.product-specs-table tbody tr:last-child {
    border-bottom: none;
}

.product-specs-table .spec-label {
    background-color: #f9f9f9;
    padding: 12px 15px;
    font-weight: 600;
    color: #555;
    width: 30%;
    vertical-align: top;
    border-right: 1px solid #eee;
}

.product-specs-table .spec-value {
    background-color: #fff;
    padding: 12px 15px;
    color: #333;
    width: 70%;
    vertical-align: top;
    line-height: 1.5;
}

.product-specs-table .spec-value ul {
    margin: 10px 0;
    padding-left: 20px;
}

.product-specs-table .spec-value ul li {
    margin-bottom: 5px;
}

.product-specs-table .spec-value h6 {
    color: #333;
    font-weight: 600;
    margin: 10px 0 8px 0;
    font-size: 14px;
}

.product-specs-table .spec-value .table {
    margin: 10px 0;
    font-size: 13px;
    border: 1px solid #eee;
}

.product-specs-table .spec-value .table td {
    padding: 8px 12px;
    border-top: 1px solid #eee;
}

.product-specs-table .spec-value strong {
    color: #333;
    font-weight: 600;
}

.product-specs-table .spec-value .row {
    margin: 0;
}

.product-specs-table .spec-value .col-md-6 {
    padding: 0 10px;
}

/* Hidden state */
.product-extra-info-content.hidden {
    display: none;
}

/* Toggle button states */
.toggle-extra-info .icon {
    margin-left: 5px;
    transition: transform 0.2s;
}

.toggle-extra-info.collapsed .icon {
    transform: rotate(180deg);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .product-extra-info-header {
        padding: 8px 12px;
    }

    .product-extra-info-header h3 {
        font-size: 15px;
    }

    .product-extra-info-content {
        padding: 12px;
    }

    .product-specs-table .spec-label,
    .product-specs-table .spec-value {
        padding: 10px 12px;
        font-size: 13px;
    }

    .product-specs-table .spec-label {
        width: 35%;
    }

    .product-specs-table .spec-value {
        width: 65%;
    }
}

@media (max-width: 576px) {
    .product-extra-info-header {
        padding: 8px 10px;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .product-extra-info-header h3 {
        font-size: 14px;
    }

    .toggle-extra-info {
        font-size: 12px;
        padding: 3px 8px;
    }

    .product-extra-info-content {
        padding: 10px;
    }

    .product-specs-table {
        font-size: 12px;
    }

    .product-specs-table .spec-label,
    .product-specs-table .spec-value {
        padding: 8px 10px;
    }

    /* Stack labels and values on very small screens */
    .product-specs-table .spec-label {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #eee;
        background-color: #f0f0f0;
    }

    .product-specs-table .spec-value {
        width: 100%;
    }

    .product-specs-table tbody tr {
        display: block;
        margin-bottom: 10px;
        border: 1px solid #ddd;
    }

    .product-specs-table tbody td {
        display: block;
        border: none;
    }
}

/* Print styles */
@media print {
    .product-extra-info {
        border: 1px solid #000;
        break-inside: avoid;
    }

    .toggle-extra-info {
        display: none;
    }

    .product-extra-info-content {
        display: block !important;
    }

    .product-specs-table .spec-label {
        background-color: #f0f0f0 !important;
    }
}
