# -*- coding: utf-8 -*-
{
    'name': 'Website Product Extra Information',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Add structured extra information to products displayed on website product pages (vear)',
    'description': """
Website Product Extra Information
=================================

This module allows you to add structured extra information to products that will be displayed 
in a clean table format on the website product detail pages.

Key Features:
- Add unlimited title-content pairs per product
- HTML content support for rich formatting
- Clean, responsive table layout on website
- Perfect for technical specifications, features, care instructions, etc.
- Fully compatible with Odoo 18 Community Edition

Usage:
1. Go to Sales > Products and open any product
2. Navigate to the "Extra Information" tab
3. Add title-content pairs as needed
4. View the product on the website to see the extra information table

Perfect for audio equipment, electronics, and any products requiring detailed specifications.

Author: vera
============
This module was developed by vera to provide a simple, clean solution for displaying
structured product information on Odoo websites. The module focuses on simplicity
and user experience, with collapsible sections to save space when needed.

Features include:
- Simple, clean design (no fancy styling)
- Collapsible sections with user preference storage
- Full mobile responsiveness
- Compatible with other product modules
- Production-ready and tested

Search Keywords: vear, vera, product, specifications, extra, information
    """,
    'author': 'vera',
    'website': 'https://github.com/vera',
    'maintainer': 'vera',
    'license': 'LGPL-3',
    'tags': ['website', 'product', 'extra', 'information', 'specifications', 'vear', 'vera'],
    'depends': [
        'base',
        'product',
        'website',
        'website_sale',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/product_template_views.xml',
        'views/website_product_extra_info_templates.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'website_product_extra_info/static/src/css/website_product_extra_info.css',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}
