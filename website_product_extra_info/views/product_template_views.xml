<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <!-- Product Extra Info Form View -->
    <record id="view_product_extra_info_form" model="ir.ui.view">
        <field name="name">product.extra.info.form</field>
        <field name="model">product.extra.info</field>
        <field name="arch" type="xml">
            <form string="Product Extra Information">
                <sheet>
                    <group>
                        <field name="sequence" widget="handle"/>
                        <field name="name" placeholder="e.g., Specifications, Features, Care Instructions"/>
                        <field name="content" widget="html" placeholder="Enter detailed information here..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Extra Info List View -->
    <record id="view_product_extra_info_list" model="ir.ui.view">
        <field name="name">product.extra.info.list</field>
        <field name="model">product.extra.info</field>
        <field name="arch" type="xml">
            <list string="Product Extra Information" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="content" widget="html"/>
            </list>
        </field>
    </record>

    <!-- Extend Product Template Form View - Add Extra Information Tab -->
    <record id="view_product_template_form_extra_info" model="ir.ui.view">
        <field name="name">product.template.form.extra.info</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <!-- Add as a new page in the main notebook -->
            <xpath expr="//notebook/page[@name='general_information']" position="after">
                <page string="Extra Information" name="extra_information">
                    <group>
                        <field name="extra_info_ids" context="{'default_product_tmpl_id': id}" nolabel="1">
                            <list editable="bottom" decoration-info="True">
                                <field name="sequence" widget="handle"/>
                                <field name="name" placeholder="Title (e.g., Specifications, Features, Care Instructions)" required="1"/>
                                <field name="content" widget="html" placeholder="Enter detailed information here..." required="1"/>
                            </list>
                            <form>
                                <sheet>
                                    <group>
                                        <field name="sequence"/>
                                        <field name="name" placeholder="e.g., Specifications, Features, Care Instructions" required="1"/>
                                    </group>
                                    <group>
                                        <field name="content" widget="html" placeholder="Enter detailed information here..." required="1"/>
                                    </group>
                                </sheet>
                            </form>
                        </field>
                    </group>
                    <div class="alert alert-info mt-3" role="alert">
                        <strong>Info:</strong> This extra information will be displayed on the website product page to provide additional details to customers.
                    </div>
                </page>
            </xpath>
        </field>
    </record>

</odoo>
