<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Include CSS Assets -->
    <template id="product_extra_info_assets" name="Product Extra Info Assets" inherit_id="website.layout">
        <xpath expr="//head" position="inside">
            <link rel="stylesheet" type="text/css" href="/website_product_extra_info/static/src/css/website_product_extra_info.css"/>
        </xpath>
    </template>

    <!-- Extend Website Product Detail Page -->
    <template id="product_extra_info_display" name="Product Extra Information Display" inherit_id="website_sale.product" priority="20">
        <xpath expr="//div[@id='product_detail_main']" position="after">
            <t t-if="product.has_extra_info()">
                <!-- Simple collapsible section for product information -->
                <div class="container mt-4">
                    <div class="row">
                        <div class="col-12">
                            <div class="product-extra-info" id="product-extra-info-section">
                                <div class="product-extra-info-header">
                                    <h3>Product Specifications</h3>
                                    <button type="button" class="toggle-extra-info" onclick="if(typeof toggleExtraInfo === 'function') toggleExtraInfo(); else console.error('toggleExtraInfo not available');">
                                        <span class="text">Hide</span>
                                        <span class="icon">▲</span>
                                    </button>
                                </div>
                                <div class="product-extra-info-content" id="extraInfoContent">
                                    <table class="product-specs-table">
                                        <tbody>
                                            <t t-foreach="product.extra_info_ids" t-as="info">
                                                <tr>
                                                    <td class="spec-label">
                                                        <span t-field="info.name"/>
                                                    </td>
                                                    <td class="spec-value">
                                                        <div t-field="info.content"/>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Inline JavaScript to avoid loading issues -->
                            <script type="text/javascript">
                                // Simple Product Extra Info Toggle Functionality
                                function toggleExtraInfo() {
                                    try {
                                        const content = document.getElementById('extraInfoContent');
                                        const button = document.querySelector('.toggle-extra-info');

                                        if (!content || !button) {
                                            console.warn('Product extra info elements not found');
                                            return;
                                        }

                                        const textSpan = button.querySelector('.text');
                                        const iconSpan = button.querySelector('.icon');

                                        if (content.classList.contains('hidden')) {
                                            // Show content
                                            content.classList.remove('hidden');
                                            button.classList.remove('collapsed');
                                            if (textSpan) textSpan.textContent = 'Hide';
                                            if (iconSpan) iconSpan.textContent = '▲';

                                            // Save preference
                                            try {
                                                localStorage.setItem('productExtraInfoVisible', 'true');
                                            } catch (e) {
                                                console.warn('Could not save preference to localStorage');
                                            }
                                        } else {
                                            // Hide content
                                            content.classList.add('hidden');
                                            button.classList.add('collapsed');
                                            if (textSpan) textSpan.textContent = 'Show';
                                            if (iconSpan) iconSpan.textContent = '▼';

                                            // Save preference
                                            try {
                                                localStorage.setItem('productExtraInfoVisible', 'false');
                                            } catch (e) {
                                                console.warn('Could not save preference to localStorage');
                                            }
                                        }
                                    } catch (error) {
                                        console.error('Error in toggleExtraInfo:', error);
                                    }
                                }

                                // Initialize on page load
                                document.addEventListener('DOMContentLoaded', function() {
                                    try {
                                        const content = document.getElementById('extraInfoContent');
                                        const button = document.querySelector('.toggle-extra-info');

                                        if (content &amp;&amp; button) {
                                            const textSpan = button.querySelector('.text');
                                            const iconSpan = button.querySelector('.icon');

                                            // Check saved preference (default to visible)
                                            let isVisible = 'true';
                                            try {
                                                isVisible = localStorage.getItem('productExtraInfoVisible') || 'true';
                                            } catch (e) {
                                                console.warn('Could not read preference from localStorage');
                                            }

                                            if (isVisible === 'false') {
                                                // Hide by default if user previously chose to hide
                                                content.classList.add('hidden');
                                                button.classList.add('collapsed');
                                                if (textSpan) textSpan.textContent = 'Show';
                                                if (iconSpan) iconSpan.textContent = '▼';
                                            } else {
                                                // Show by default
                                                content.classList.remove('hidden');
                                                button.classList.remove('collapsed');
                                                if (textSpan) textSpan.textContent = 'Hide';
                                                if (iconSpan) iconSpan.textContent = '▲';
                                            }
                                        }
                                    } catch (error) {
                                        console.error('Error initializing product extra info:', error);
                                    }
                                });
                            </script>
                        </div>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

</odoo>
