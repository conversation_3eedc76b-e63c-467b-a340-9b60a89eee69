#!/usr/bin/env python3
"""
Test script to verify bi_website_hide_price module backend functionality
"""

import subprocess
import sys
import time

def run_odoo_command(command):
    """Run an Odoo command in the container"""
    full_command = f"docker exec odoo-odoo-1 {command}"
    try:
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"

def test_module_installation():
    """Test if the module is properly installed"""
    print("🔍 Testing module installation...")
    
    # Test with a simple Python command
    python_code = '''
import odoo
from odoo import api, SUPERUSER_ID
from odoo.tools import config

# Set up minimal config
config['db_name'] = 'odoo'

try:
    with api.Environment.manage():
        registry = odoo.registry('odoo')
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # Check if module is installed
            module = env['ir.module.module'].search([('name', '=', 'bi_website_hide_price')])
            if module:
                print(f"✅ Module found: {module.name}")
                print(f"✅ Module state: {module.state}")
                
                # Check if product fields exist
                product_fields = env['product.template'].fields_get(['website_hide_price', 'website_hide_price_message'])
                if 'website_hide_price' in product_fields:
                    print("✅ Product field 'website_hide_price' exists")
                if 'website_hide_price_message' in product_fields:
                    print("✅ Product field 'website_hide_price_message' exists")
                    
                # Test creating a product with hide price
                product = env['product.template'].create({
                    'name': 'Test Product for Price Hiding',
                    'list_price': 100.0,
                    'website_hide_price': True,
                    'website_hide_price_message': 'Contact us for special pricing!'
                })
                print(f"✅ Created test product: {product.name}")
                print(f"✅ Hide price enabled: {product.website_hide_price}")
                print(f"✅ Custom message: {product.website_hide_price_message}")
                
                # Test the _should_hide_price method
                should_hide = product._should_hide_price()
                print(f"✅ Should hide price method result: {should_hide}")
                
                # Test the _get_hide_price_message method
                message = product._get_hide_price_message()
                print(f"✅ Hide price message: {message}")
                
            else:
                print("❌ Module not found")
                
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    # Write the test script to a temporary file
    with open('/tmp/test_module.py', 'w') as f:
        f.write(python_code)
    
    # Copy the script to the container and run it
    copy_cmd = "docker cp /tmp/test_module.py odoo-odoo-1:/tmp/test_module.py"
    subprocess.run(copy_cmd, shell=True)
    
    # Run the test script
    returncode, stdout, stderr = run_odoo_command("python3 /tmp/test_module.py")
    
    print("📋 Test Results:")
    print("-" * 50)
    if stdout:
        print(stdout)
    if stderr:
        print("Errors:")
        print(stderr)
    
    return returncode == 0

def test_web_interface():
    """Test if the web interface is accessible"""
    print("\n🌐 Testing web interface...")
    
    try:
        import urllib.request
        response = urllib.request.urlopen('http://localhost:8069', timeout=10)
        if response.getcode() == 200:
            print("✅ Odoo web interface is accessible at http://localhost:8069")
            return True
        else:
            print(f"❌ Web interface returned status code: {response.getcode()}")
            return False
    except Exception as e:
        print(f"❌ Cannot access web interface: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("  BI_WEBSITE_HIDE_PRICE MODULE - BACKEND TESTING")
    print("="*60)
    
    # Test module installation
    module_ok = test_module_installation()
    
    # Test web interface
    web_ok = test_web_interface()
    
    print("\n" + "="*60)
    print("  TESTING SUMMARY")
    print("="*60)
    
    if module_ok:
        print("✅ Backend functionality: WORKING")
    else:
        print("❌ Backend functionality: FAILED")
    
    if web_ok:
        print("✅ Web interface: ACCESSIBLE")
    else:
        print("❌ Web interface: NOT ACCESSIBLE")
    
    if module_ok and web_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 Next steps:")
        print("1. Open http://localhost:8069 in your browser")
        print("2. Login with admin/admin")
        print("3. Go to Sales > Products > Products")
        print("4. Look for the test product 'Test Product for Price Hiding'")
        print("5. Check the 'Sales' tab for price hiding fields")
        return True
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the errors above and try again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
