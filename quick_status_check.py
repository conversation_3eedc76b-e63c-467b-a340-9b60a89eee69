#!/usr/bin/env python3
"""
Quick status check for bi_website_hide_price module
"""

import subprocess
import urllib.request
import socket

def check_docker_status():
    """Check if Docker containers are running"""
    try:
        result = subprocess.run(['docker-compose', 'ps'], capture_output=True, text=True, cwd='.')
        if 'odoo-odoo-1' in result.stdout and 'Up' in result.stdout:
            return True, "Docker containers are running"
        else:
            return False, "Docker containers not running properly"
    except Exception as e:
        return False, f"Error checking Docker: {e}"

def check_web_access():
    """Check if Odoo web interface is accessible"""
    try:
        response = urllib.request.urlopen('http://localhost:8069', timeout=5)
        if response.getcode() == 200:
            return True, "Odoo web interface is accessible"
        else:
            return False, f"Web interface returned status {response.getcode()}"
    except Exception as e:
        return False, f"Cannot access web interface: {e}"

def check_module_files():
    """Check if module files are present"""
    try:
        result = subprocess.run(['docker', 'exec', 'odoo-odoo-1', 'ls', '/mnt/extra-addons/bi_website_hide_price/'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and '__manifest__.py' in result.stdout:
            return True, "Module files are properly mounted"
        else:
            return False, "Module files not found or not mounted"
    except Exception as e:
        return False, f"Error checking module files: {e}"

def main():
    print("🔍 BI_WEBSITE_HIDE_PRICE - QUICK STATUS CHECK")
    print("=" * 50)
    
    checks = [
        ("Docker Status", check_docker_status),
        ("Web Access", check_web_access),
        ("Module Files", check_module_files),
    ]
    
    all_good = True
    
    for check_name, check_func in checks:
        try:
            status, message = check_func()
            icon = "✅" if status else "❌"
            print(f"{icon} {check_name}: {message}")
            if not status:
                all_good = False
        except Exception as e:
            print(f"❌ {check_name}: Error - {e}")
            all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 ALL SYSTEMS GO!")
        print("\n📋 Ready for manual testing:")
        print("1. Open http://localhost:8069")
        print("2. Login with admin/admin")
        print("3. Follow the MANUAL_TESTING_STEPS.md guide")
        print("\n🔗 Browser should already be open with Odoo")
    else:
        print("⚠️  SOME ISSUES DETECTED")
        print("Please fix the issues above before testing")
        
        # Try to restart if needed
        print("\n🔄 Attempting to restart Odoo...")
        try:
            subprocess.run(['docker-compose', 'restart', 'odoo'], cwd='.')
            print("✅ Restart command sent")
            print("⏳ Wait 30 seconds and run this script again")
        except Exception as e:
            print(f"❌ Restart failed: {e}")

if __name__ == "__main__":
    main()
