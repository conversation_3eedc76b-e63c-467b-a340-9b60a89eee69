# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request
import json


class CategorySnippetController(http.Controller):

    @http.route('/website/category_snippet/data', type='json', auth='public', website=True)
    def get_category_data(self, style='grid', limit=8, show_count=True, show_description=True, **kwargs):
        """Get category data for snippets"""
        try:
            category_data = []

            # Try to get real categories first
            try:
                # Check if product.public.category model exists
                if 'product.public.category' in request.env:
                    categories = request.env['product.public.category'].sudo().search([
                        ('parent_id', '=', False)
                    ], limit=limit)

                    for category in categories:
                        # Count products in category
                        product_count = len(category.product_tmpl_ids) if hasattr(category, 'product_tmpl_ids') and show_count else 0

                        # Get category image
                        image_url = '/web/image/product.public.category/%s/image_1920' % category.id if hasattr(category, 'image_1920') and category.image_1920 else '/website_category_snippet_builder/static/src/img/default_category.png'

                        # Get description
                        description = getattr(category, 'description', '') or self._get_default_description(category.name) if show_description else ''

                        category_data.append({
                            'id': category.id,
                            'name': category.name,
                            'description': description[:100] + '...' if len(description) > 100 else description,
                            'product_count': product_count,
                            'image_url': image_url,
                            'shop_url': '/shop?category=%s' % category.id,
                        })

            except Exception as e:
                print(f"Error loading real categories: {e}")

            # If no real categories found, use mock data
            if not category_data:
                category_data = self._get_mock_categories(limit, show_count, show_description)

            return {
                'categories': category_data,
                'style': style,
                'success': True
            }

        except Exception as e:
            # Return mock data as fallback
            return {
                'categories': self._get_mock_categories(limit, show_count, show_description),
                'style': style,
                'success': True,
                'fallback': True
            }

    def _get_mock_categories(self, limit=8, show_count=True, show_description=True):
        """Get mock category data for testing"""
        mock_categories = [
            {
                'id': 1,
                'name': 'Electronics',
                'description': 'Discover the latest in electronic devices and gadgets for modern living.',
                'product_count': 45,
                'image_url': 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=electronics',
            },
            {
                'id': 2,
                'name': 'Clothing',
                'description': 'Fashion and apparel for every style and occasion.',
                'product_count': 78,
                'image_url': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=clothing',
            },
            {
                'id': 3,
                'name': 'Home & Garden',
                'description': 'Everything you need for your home and garden improvement.',
                'product_count': 32,
                'image_url': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=home',
            },
            {
                'id': 4,
                'name': 'Sports',
                'description': 'Sports equipment and accessories for active lifestyles.',
                'product_count': 56,
                'image_url': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=sports',
            },
            {
                'id': 5,
                'name': 'Books',
                'description': 'Explore our collection of books and literature.',
                'product_count': 123,
                'image_url': 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=books',
            },
            {
                'id': 6,
                'name': 'Toys',
                'description': 'Fun and educational toys for children of all ages.',
                'product_count': 67,
                'image_url': 'https://images.unsplash.com/photo-1558060370-d644479cb6f7?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=toys',
            },
            {
                'id': 7,
                'name': 'Beauty',
                'description': 'Beauty products and cosmetics for your daily routine.',
                'product_count': 89,
                'image_url': 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=beauty',
            },
            {
                'id': 8,
                'name': 'Automotive',
                'description': 'Car parts, accessories, and automotive supplies.',
                'product_count': 34,
                'image_url': 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=automotive',
            },
            {
                'id': 9,
                'name': 'Health',
                'description': 'Health and wellness products for a better lifestyle.',
                'product_count': 41,
                'image_url': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=health',
            },
            {
                'id': 10,
                'name': 'Technology',
                'description': 'Latest technology gadgets and innovative solutions.',
                'product_count': 52,
                'image_url': 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop',
                'shop_url': '/shop?search=technology',
            },
        ]

        # Limit the results
        limited_categories = mock_categories[:limit]

        # Process based on display options
        for category in limited_categories:
            if not show_description:
                category['description'] = ''
            if not show_count:
                category['product_count'] = 0

        return limited_categories

    def _get_default_description(self, category_name):
        """Get default description for categories"""
        descriptions = {
            'Electronics': 'Discover the latest in electronic devices and gadgets.',
            'Clothing': 'Fashion and apparel for every style and occasion.',
            'Home & Garden': 'Everything you need for your home and garden.',
            'Sports': 'Sports equipment and accessories for active lifestyles.',
            'Books': 'Explore our collection of books and literature.',
            'Toys': 'Fun and educational toys for children of all ages.',
            'Beauty': 'Beauty products and cosmetics for your daily routine.',
            'Automotive': 'Car parts, accessories, and automotive supplies.',
        }
        return descriptions.get(category_name, f'Explore our {category_name.lower()} collection.')

    @http.route('/website/category_snippet/render', type='http', auth='public', website=True)
    def render_category_snippet(self, style='grid', limit=8, show_count=True, show_description=True, **kwargs):
        """Render category snippet HTML"""
        try:
            data = self.get_category_data(style, int(limit), show_count == 'true', show_description == 'true')
            
            if data['success']:
                return request.render('website_category_snippet_builder.category_snippet_template', {
                    'categories': data['categories'],
                    'style': style,
                    'show_count': show_count == 'true',
                    'show_description': show_description == 'true',
                })
            else:
                return request.render('website_category_snippet_builder.category_snippet_error', {
                    'error': data.get('error', 'Unknown error')
                })
                
        except Exception as e:
            return request.render('website_category_snippet_builder.category_snippet_error', {
                'error': str(e)
            })
