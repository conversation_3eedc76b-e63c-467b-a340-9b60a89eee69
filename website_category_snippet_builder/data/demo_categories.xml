<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- eCommerce Product Categories -->

        <!-- Electronics Category -->
        <record id="category_electronics" model="product.public.category">
            <field name="name">Electronics</field>
            <field name="sequence">10</field>
        </record>

        <!-- Clothing Category -->
        <record id="category_clothing" model="product.public.category">
            <field name="name">Clothing</field>
            <field name="sequence">20</field>
        </record>

        <!-- Home & Garden Category -->
        <record id="category_home_garden" model="product.public.category">
            <field name="name">Home &amp; Garden</field>
            <field name="sequence">30</field>
        </record>

        <!-- Sports Category -->
        <record id="category_sports" model="product.public.category">
            <field name="name">Sports</field>
            <field name="sequence">40</field>
        </record>

        <!-- Books Category -->
        <record id="category_books" model="product.public.category">
            <field name="name">Books</field>
            <field name="sequence">50</field>
        </record>

        <!-- Toys Category -->
        <record id="category_toys" model="product.public.category">
            <field name="name">Toys</field>
            <field name="sequence">60</field>
        </record>

        <!-- Beauty Category -->
        <record id="category_beauty" model="product.public.category">
            <field name="name">Beauty</field>
            <field name="sequence">70</field>
        </record>

        <!-- Automotive Category -->
        <record id="category_automotive" model="product.public.category">
            <field name="name">Automotive</field>
            <field name="sequence">80</field>
        </record>

        <!-- Technology Category -->
        <record id="category_technology" model="product.public.category">
            <field name="name">Technology</field>
            <field name="sequence">90</field>
        </record>

        <!-- Health Category -->
        <record id="category_health" model="product.public.category">
            <field name="name">Health</field>
            <field name="sequence">100</field>
        </record>

    </data>
</odoo>
