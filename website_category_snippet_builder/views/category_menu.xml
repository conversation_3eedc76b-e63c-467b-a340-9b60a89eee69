<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add menu item for managing eCommerce categories -->
    <record id="action_product_public_category" model="ir.actions.act_window">
        <field name="name">eCommerce Categories</field>
        <field name="res_model">product.public.category</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="website_sale.product_public_category_view_tree"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first eCommerce category!
            </p>
            <p>
                Categories help organize your products and make it easier for customers to find what they're looking for.
                You can add images, descriptions, and organize them hierarchically.
            </p>
        </field>
    </record>

    <!-- Add menu item under Website -->
    <menuitem id="menu_product_public_category"
              name="eCommerce Categories"
              parent="website_sale.menu_catalog"
              action="action_product_public_category"
              sequence="10"/>


</odoo>
