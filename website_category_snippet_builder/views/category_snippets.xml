<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Category Snippet Style 1: Classic Grid -->
    <template id="s_category_grid_1" name="Category Grid Style 1">
        <section class="s_category_snippet s_category_grid_1 pt64 pb64" data-snippet="s_category_grid_1" data-name="Category Grid 1">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5">
                        <h2 class="h2 mb-3">🛍️ Shop by Category</h2>
                        <p class="lead mb-4">Find exactly what you're looking for - Browse our categories for quick and easy shopping</p>
                        <!-- Quick Search Bar -->
                        <div class="category-search-bar mb-4">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-primary text-white">
                                            <i class="fa fa-search"></i>
                                        </span>
                                        <input type="text" class="form-control" placeholder="Search categories or products..." id="categorySearchInput"/>
                                        <button class="btn btn-primary" type="button" onclick="searchCategories()">
                                            <i class="fa fa-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Navigation -->
                        <div class="category-quick-nav mb-4">
                            <a href="/shop" class="btn btn-outline-primary me-2 mb-2">
                                <i class="fa fa-th-large me-1"></i> All Products
                            </a>
                            <a href="/shop?search=new" class="btn btn-outline-success me-2 mb-2">
                                <i class="fa fa-star me-1"></i> New Arrivals
                            </a>
                            <a href="/shop?search=sale" class="btn btn-outline-danger me-2 mb-2">
                                <i class="fa fa-fire me-1"></i> On Sale
                            </a>
                            <a href="/shop?search=popular" class="btn btn-outline-info me-2 mb-2">
                                <i class="fa fa-heart me-1"></i> Popular
                            </a>
                            <a href="/shop?search=featured" class="btn btn-outline-warning mb-2">
                                <i class="fa fa-trophy me-1"></i> Featured
                            </a>
                        </div>

                        <!-- Category Filter Tabs -->
                        <div class="category-filter-tabs mb-4">
                            <ul class="nav nav-pills justify-content-center" id="categoryTabs">
                                <li class="nav-item">
                                    <a class="nav-link active" href="#" onclick="filterCategories('all')">
                                        <i class="fa fa-th me-1"></i> All Categories
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="filterCategories('popular')">
                                        <i class="fa fa-fire me-1"></i> Most Popular
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="#" onclick="filterCategories('recent')">
                                        <i class="fa fa-clock me-1"></i> Recently Added
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="row" id="categoryContainer">
                    <!-- Server-side rendered categories with fallback -->
                    <t t-set="categories" t-value="request.env['product.public.category'].sudo().search([('parent_id', '=', False)], limit=8, order='sequence, name')"/>
                    <t t-if="categories and len(categories) > 0">
                        <t t-foreach="categories" t-as="category">
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="category-item-grid-1 h-100">
                                    <div class="category-card-wrapper h-100">
                                        <div class="category-image-wrapper position-relative">
                                            <img t-if="category.image_1920" t-attf-src="/web/image/product.public.category/#{category.id}/image_1920" t-attf-alt="#{category.name}" class="category-image"/>
                                            <img t-else="" t-attf-src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&amp;h=300&amp;fit=crop&amp;q=#{category.id % 10}" t-attf-alt="#{category.name}" class="category-image"/>
                                            <div class="category-overlay">
                                                <div class="category-quick-actions">
                                                    <span t-attf-class="badge category-count-badge bg-#{['primary', 'success', 'info', 'warning', 'danger'][category.id % 5]}">
                                                        <i class="fa fa-cube me-1"></i><t t-esc="len(category.product_tmpl_ids)"/> Items
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="category-content text-center p-3">
                                            <h4 class="category-title mb-2">
                                                <t t-if="category.name == 'Electronics'">📱</t>
                                                <t t-elif="category.name == 'Clothing'">👕</t>
                                                <t t-elif="category.name == 'Home &amp; Garden'">🏠</t>
                                                <t t-elif="category.name == 'Sports'">⚽</t>
                                                <t t-elif="category.name == 'Books'">📚</t>
                                                <t t-elif="category.name == 'Toys'">🧸</t>
                                                <t t-elif="category.name == 'Beauty'">💄</t>
                                                <t t-elif="category.name == 'Automotive'">🚗</t>
                                                <t t-elif="category.name == 'Technology'">💻</t>
                                                <t t-elif="category.name == 'Health'">🏥</t>
                                                <t t-else="">🛍️</t>
                                                <t t-esc="category.name"/>
                                            </h4>
                                            <p class="category-description text-muted small mb-2" t-esc="'Explore our %s collection.' % category.name.lower()"/>

                                            <!-- Category Stats -->
                                            <div class="category-stats mb-3">
                                                <small class="text-success me-2">
                                                    <i class="fa fa-check-circle me-1"></i>In Stock
                                                </small>
                                                <small class="text-info">
                                                    <i class="fa fa-truck me-1"></i>Fast Shipping
                                                </small>
                                            </div>

                                            <div class="category-actions">
                                                <a t-attf-href="/shop?category=#{category.id}" class="btn btn-primary btn-sm category-shop-btn">
                                                    <i class="fa fa-shopping-cart me-1"></i> Shop Now
                                                </a>
                                                <a t-attf-href="/shop?category=#{category.id}" class="btn btn-outline-secondary btn-sm ms-1 category-view-btn">
                                                    <i class="fa fa-eye me-1"></i> View All
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <!-- Fallback static categories -->
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="category-item-grid-1 h-100">
                                <div class="category-card-wrapper h-100">
                                    <div class="category-image-wrapper position-relative">
                                        <img src="https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&amp;h=300&amp;fit=crop" alt="Electronics" class="category-image"/>
                                        <div class="category-overlay">
                                            <div class="category-quick-actions">
                                                <span class="badge bg-primary category-count-badge">
                                                    <i class="fa fa-cube me-1"></i>45 Items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content text-center p-3">
                                        <h4 class="category-title mb-2">📱 Electronics</h4>
                                        <p class="category-description text-muted small mb-3">Explore our electronics collection.</p>
                                        <div class="category-actions">
                                            <a href="/shop?search=electronics" class="btn btn-primary btn-sm category-shop-btn">
                                                <i class="fa fa-shopping-cart me-1"></i> Shop Now
                                            </a>
                                            <a href="/shop?search=electronics" class="btn btn-outline-secondary btn-sm ms-1 category-view-btn">
                                                <i class="fa fa-eye me-1"></i> View All
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="category-item-grid-1 h-100">
                                <div class="category-card-wrapper h-100">
                                    <div class="category-image-wrapper position-relative">
                                        <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&amp;h=300&amp;fit=crop" alt="Clothing" class="category-image"/>
                                        <div class="category-overlay">
                                            <div class="category-quick-actions">
                                                <span class="badge bg-success category-count-badge">
                                                    <i class="fa fa-cube me-1"></i>78 Items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content text-center p-3">
                                        <h4 class="category-title mb-2">👕 Clothing</h4>
                                        <p class="category-description text-muted small mb-3">Explore our clothing collection.</p>
                                        <div class="category-actions">
                                            <a href="/shop?search=clothing" class="btn btn-success btn-sm category-shop-btn">
                                                <i class="fa fa-shopping-cart me-1"></i> Shop Now
                                            </a>
                                            <a href="/shop?search=clothing" class="btn btn-outline-secondary btn-sm ms-1 category-view-btn">
                                                <i class="fa fa-eye me-1"></i> View All
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="category-item-grid-1 h-100">
                                <div class="category-card-wrapper h-100">
                                    <div class="category-image-wrapper position-relative">
                                        <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&amp;h=300&amp;fit=crop" alt="Home &amp; Garden" class="category-image"/>
                                        <div class="category-overlay">
                                            <div class="category-quick-actions">
                                                <span class="badge bg-info category-count-badge">
                                                    <i class="fa fa-cube me-1"></i>32 Items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content text-center p-3">
                                        <h4 class="category-title mb-2">🏠 Home &amp; Garden</h4>
                                        <p class="category-description text-muted small mb-3">Explore our home &amp; garden collection.</p>
                                        <div class="category-actions">
                                            <a href="/shop?search=home" class="btn btn-info btn-sm category-shop-btn">
                                                <i class="fa fa-shopping-cart me-1"></i> Shop Now
                                            </a>
                                            <a href="/shop?search=home" class="btn btn-outline-secondary btn-sm ms-1 category-view-btn">
                                                <i class="fa fa-eye me-1"></i> View All
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="category-item-grid-1 h-100">
                                <div class="category-card-wrapper h-100">
                                    <div class="category-image-wrapper position-relative">
                                        <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&amp;h=300&amp;fit=crop" alt="Sports" class="category-image"/>
                                        <div class="category-overlay">
                                            <div class="category-quick-actions">
                                                <span class="badge bg-warning category-count-badge">
                                                    <i class="fa fa-cube me-1"></i>56 Items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="category-content text-center p-3">
                                        <h4 class="category-title mb-2">⚽ Sports</h4>
                                        <p class="category-description text-muted small mb-3">Explore our sports collection.</p>
                                        <div class="category-actions">
                                            <a href="/shop?search=sports" class="btn btn-warning btn-sm category-shop-btn">
                                                <i class="fa fa-shopping-cart me-1"></i> Shop Now
                                            </a>
                                            <a href="/shop?search=sports" class="btn btn-outline-secondary btn-sm ms-1 category-view-btn">
                                                <i class="fa fa-eye me-1"></i> View All
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </div>

                <!-- Enhanced JavaScript for Quick Access -->
                <script>
                    function searchCategories() {
                        var searchTerm = document.getElementById('categorySearchInput').value;
                        if (searchTerm.trim()) {
                            window.location.href = '/shop?search=' + encodeURIComponent(searchTerm);
                        }
                    }

                    function filterCategories(filter) {
                        // Update active tab
                        var tabs = document.querySelectorAll('#categoryTabs .nav-link');
                        tabs.forEach(function(tab) {
                            tab.classList.remove('active');
                        });
                        event.target.classList.add('active');

                        // Filter categories based on selection
                        var categories = document.querySelectorAll('.category-item-grid-1');
                        categories.forEach(function(category, index) {
                            var shouldShow = true;

                            if (filter === 'popular') {
                                // Show categories with higher product counts
                                var countBadge = category.querySelector('.category-count-badge');
                                if (countBadge) {
                                    var countText = countBadge.textContent;
                                    var countMatch = countText.match(/\d+/);
                                    if (countMatch) {
                                        var count = parseInt(countMatch[0]);
                                        shouldShow = count &gt; 30;
                                    }
                                }
                            } else if (filter === 'recent') {
                                // Show first 4 categories as recent
                                shouldShow = index &lt; 4;
                            }

                            if (shouldShow) {
                                category.closest('.col-lg-3').style.display = 'block';
                                category.closest('.col-lg-3').style.animation = 'fadeIn 0.5s ease';
                            } else {
                                category.closest('.col-lg-3').style.display = 'none';
                            }
                        });
                    }

                    // Search on Enter key
                    document.addEventListener('DOMContentLoaded', function() {
                        var searchInput = document.getElementById('categorySearchInput');
                        if (searchInput) {
                            searchInput.addEventListener('keypress', function(e) {
                                if (e.key === 'Enter') {
                                    searchCategories();
                                }
                            });
                        }
                    });
                </script>

                <!-- CSS for animations -->
                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    .category-search-bar .input-group {
                        border-radius: 25px;
                        overflow: hidden;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }

                    .category-search-bar .form-control {
                        border: none;
                        font-size: 1.1rem;
                        padding: 0.8rem 1rem;
                    }

                    .category-search-bar .form-control:focus {
                        box-shadow: none;
                        border-color: transparent;
                    }

                    .category-filter-tabs .nav-pills .nav-link {
                        border-radius: 20px;
                        margin: 0 0.2rem;
                        font-weight: 500;
                        transition: all 0.3s ease;
                    }

                    .category-filter-tabs .nav-pills .nav-link:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    }

                    .category-filter-tabs .nav-pills .nav-link.active {
                        background: linear-gradient(45deg, #007bff, #0056b3);
                        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
                    }
                </style>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 2: Card Layout -->
    <template id="s_category_cards_2" name="Category Cards Style 2">
        <section class="s_category_snippet s_category_cards_2 pt64 pb64" data-snippet="s_category_cards_2" data-name="Category Cards 2">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Browse Categories</h2>
                        <p class="lead">Find what you're looking for</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="cards_2" data-limit="6">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 3: Circular Icons -->
    <template id="s_category_circles_3" name="Category Circles Style 3">
        <section class="s_category_snippet s_category_circles_3 pt64 pb64" data-snippet="s_category_circles_3" data-name="Category Circles 3">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Product Categories</h2>
                        <p class="lead">Explore our collections</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="circles_3" data-limit="8">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 4: Overlay Design -->
    <template id="s_category_overlay_4" name="Category Overlay Style 4">
        <section class="s_category_snippet s_category_overlay_4 pt64 pb64" data-snippet="s_category_overlay_4" data-name="Category Overlay 4">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Shop Collections</h2>
                        <p class="lead">Discover amazing products</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="overlay_4" data-limit="6">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 5: Minimal Design -->
    <template id="s_category_minimal_5" name="Category Minimal Style 5">
        <section class="s_category_snippet s_category_minimal_5 pt64 pb64" data-snippet="s_category_minimal_5" data-name="Category Minimal 5">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Categories</h2>
                    </div>
                </div>
                <div class="row category-grid" data-style="minimal_5" data-limit="8">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 6: Banner Style -->
    <template id="s_category_banner_6" name="Category Banner Style 6">
        <section class="s_category_snippet s_category_banner_6 pt64 pb64" data-snippet="s_category_banner_6" data-name="Category Banner 6">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Featured Categories</h2>
                        <p class="lead">Top product collections</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="banner_6" data-limit="4">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 7: List View -->
    <template id="s_category_list_7" name="Category List Style 7">
        <section class="s_category_snippet s_category_list_7 pt64 pb64" data-snippet="s_category_list_7" data-name="Category List 7">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">All Categories</h2>
                        <p class="lead">Complete category listing</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="list_7" data-limit="10">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Category Snippet Style 8: Masonry Layout -->
    <template id="s_category_masonry_8" name="Category Masonry Style 8">
        <section class="s_category_snippet s_category_masonry_8 pt64 pb64" data-snippet="s_category_masonry_8" data-name="Category Masonry 8">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-4">
                        <h2 class="h3-fs">Explore Categories</h2>
                        <p class="lead">Dynamic category showcase</p>
                    </div>
                </div>
                <div class="row category-grid" data-style="masonry_8" data-limit="8">
                    <div class="col-12 text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading categories...</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <!-- Dynamic Category Template -->
    <template id="category_snippet_template" name="Category Snippet Template">
        <t t-foreach="categories" t-as="category">
            <t t-if="style == 'grid_1'">
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="category-item-grid-1">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-image-wrapper">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                            </div>
                            <div class="category-content">
                                <h4 class="category-title" t-esc="category['name']"/>
                                <p t-if="show_description" class="category-description" t-esc="category['description']"/>
                                <span t-if="show_count" class="category-count" t-esc="'%s Products' % category['product_count']"/>
                            </div>
                        </a>
                    </div>
                </div>
            </t>
            
            <t t-if="style == 'cards_2'">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="category-item-cards-2">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-card">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                                <div class="category-overlay">
                                    <h4 class="category-title" t-esc="category['name']"/>
                                    <p t-if="show_description" class="category-description" t-esc="category['description']"/>
                                    <span t-if="show_count" class="category-count" t-esc="'%s Items' % category['product_count']"/>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </t>
            
            <t t-if="style == 'circles_3'">
                <div class="col-lg-3 col-md-4 col-6 mb-4">
                    <div class="category-item-circles-3 text-center">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-circle">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                            </div>
                            <h5 class="category-title mt-3" t-esc="category['name']"/>
                            <span t-if="show_count" class="category-count" t-esc="'%s Products' % category['product_count']"/>
                        </a>
                    </div>
                </div>
            </t>

            <t t-if="style == 'overlay_4'">
                <div class="col-lg-6 col-md-6 mb-4">
                    <div class="category-item-overlay-4">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-overlay-wrapper">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                                <div class="category-overlay-content">
                                    <h3 class="category-title" t-esc="category['name']"/>
                                    <p t-if="show_description" class="category-description" t-esc="category['description']"/>
                                    <span t-if="show_count" class="category-count" t-esc="'%s Products' % category['product_count']"/>
                                    <div class="category-button">Shop Now</div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </t>

            <t t-if="style == 'minimal_5'">
                <div class="col-lg-2 col-md-3 col-4 mb-3">
                    <div class="category-item-minimal-5 text-center">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                            <h6 class="category-title mt-2" t-esc="category['name']"/>
                        </a>
                    </div>
                </div>
            </t>

            <t t-if="style == 'banner_6'">
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="category-item-banner-6">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-banner">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                                <div class="category-banner-content">
                                    <h2 class="category-title" t-esc="category['name']"/>
                                    <p t-if="show_description" class="category-description" t-esc="category['description']"/>
                                    <span t-if="show_count" class="category-count" t-esc="'%s Products Available' % category['product_count']"/>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </t>

            <t t-if="style == 'list_7'">
                <div class="col-lg-6 col-md-6 mb-3">
                    <div class="category-item-list-7">
                        <a t-attf-href="#{category['shop_url']}" class="category-link d-flex align-items-center">
                            <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                            <div class="category-content ms-3">
                                <h5 class="category-title mb-1" t-esc="category['name']"/>
                                <p t-if="show_description" class="category-description mb-1" t-esc="category['description']"/>
                                <span t-if="show_count" class="category-count" t-esc="'%s Products' % category['product_count']"/>
                            </div>
                        </a>
                    </div>
                </div>
            </t>

            <t t-if="style == 'masonry_8'">
                <div t-attf-class="col-lg-#{3 if category_index % 3 == 0 else 6} col-md-6 mb-4">
                    <div class="category-item-masonry-8">
                        <a t-attf-href="#{category['shop_url']}" class="category-link">
                            <div class="category-masonry">
                                <img t-attf-src="#{category['image_url']}" t-attf-alt="#{category['name']}" class="category-image"/>
                                <div class="category-masonry-content">
                                    <h4 class="category-title" t-esc="category['name']"/>
                                    <span t-if="show_count" class="category-count" t-esc="'%s Items' % category['product_count']"/>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </t>
        </t>
    </template>

    <!-- Error Template -->
    <template id="category_snippet_error" name="Category Snippet Error">
        <div class="col-12 text-center">
            <div class="alert alert-warning">
                <h5>Unable to load categories</h5>
                <p t-esc="error"/>
            </div>
        </div>
    </template>
</odoo>
