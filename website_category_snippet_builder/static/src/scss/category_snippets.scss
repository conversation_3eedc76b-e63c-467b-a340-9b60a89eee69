/* Website Category Snippet Builder Styles */

// Variables
$primary-color: #007bff;
$secondary-color: #6c757d;
$success-color: #28a745;
$info-color: #17a2b8;
$warning-color: #ffc107;
$danger-color: #dc3545;
$light-color: #f8f9fa;
$dark-color: #343a40;

// Common styles
.s_category_snippet {
    .category-link {
        text-decoration: none;
        color: inherit;
        display: block;
        
        &:hover {
            text-decoration: none;
            color: inherit;
        }
    }
    
    .category-image {
        width: 100%;
        height: auto;
        object-fit: cover;
        transition: all 0.3s ease;
    }
    
    .category-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        transition: color 0.3s ease;
    }
    
    .category-description {
        font-size: 0.9rem;
        color: $secondary-color;
        margin-bottom: 0.5rem;
    }
    
    .category-count {
        font-size: 0.8rem;
        color: $info-color;
        font-weight: 500;
    }
}

// Quick Navigation Styles
.category-quick-nav {
    .btn {
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        i {
            font-size: 0.9rem;
        }
    }
}

// Style 1: Enhanced Classic Grid
.s_category_grid_1 {
    .category-item-grid-1 {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.05);

        &:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            border-color: $primary-color;

            .category-image {
                transform: scale(1.1);
            }

            .category-overlay {
                opacity: 1;
            }

            .category-shop-btn {
                background: $success-color;
                border-color: $success-color;
            }
        }

        .category-card-wrapper {
            display: flex;
            flex-direction: column;
        }

        .category-image-wrapper {
            height: 200px;
            overflow: hidden;
            position: relative;

            .category-image {
                height: 100%;
                width: 100%;
                object-fit: cover;
                transition: transform 0.4s ease;
            }

            .category-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
                opacity: 0;
                transition: opacity 0.3s ease;
                display: flex;
                align-items: flex-start;
                justify-content: flex-end;
                padding: 1rem;
            }

            .category-count-badge {
                font-size: 0.8rem;
                font-weight: 600;
                border-radius: 20px;
                padding: 0.4rem 0.8rem;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }
        }

        .category-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .category-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: $dark-color;
                transition: color 0.3s ease;
            }

            .category-description {
                font-size: 0.85rem;
                line-height: 1.4;
                flex: 1;
            }

            .category-actions {
                margin-top: auto;

                .btn {
                    border-radius: 20px;
                    font-weight: 500;
                    font-size: 0.8rem;
                    padding: 0.4rem 0.8rem;
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
                    }
                }

                .category-shop-btn {
                    min-width: 90px;
                }

                .category-view-btn {
                    &:hover {
                        background: $light-color;
                        border-color: $secondary-color;
                    }
                }
            }
        }
    }
}

// Style 2: Card Layout
.s_category_cards_2 {
    .category-item-cards-2 {
        .category-card {
            position: relative;
            height: 250px;
            border-radius: 12px;
            overflow: hidden;
            
            .category-image {
                height: 100%;
                filter: brightness(0.8);
                transition: filter 0.3s ease;
            }
            
            .category-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0,0,0,0.8));
                color: white;
                padding: 2rem 1rem 1rem;
                
                .category-title {
                    color: white;
                    font-size: 1.2rem;
                }
                
                .category-description {
                    color: rgba(255,255,255,0.9);
                }
                
                .category-count {
                    color: rgba(255,255,255,0.8);
                }
            }
        }
        
        &:hover .category-card {
            .category-image {
                filter: brightness(1);
                transform: scale(1.1);
            }
        }
    }
}

// Style 3: Circular Icons
.s_category_circles_3 {
    .category-item-circles-3 {
        .category-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;
            border: 3px solid $light-color;
            transition: border-color 0.3s ease, transform 0.3s ease;
            
            .category-image {
                height: 100%;
            }
        }
        
        &:hover {
            .category-circle {
                border-color: $primary-color;
                transform: scale(1.1);
            }
            
            .category-title {
                color: $primary-color;
            }
        }
        
        .category-title {
            font-size: 1rem;
        }
    }
}

// Style 4: Overlay Design
.s_category_overlay_4 {
    .category-item-overlay-4 {
        .category-overlay-wrapper {
            position: relative;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            
            .category-image {
                height: 100%;
                filter: brightness(0.7);
                transition: all 0.3s ease;
            }
            
            .category-overlay-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: white;
                
                .category-title {
                    color: white;
                    font-size: 1.8rem;
                    margin-bottom: 1rem;
                }
                
                .category-description {
                    color: rgba(255,255,255,0.9);
                    margin-bottom: 1rem;
                }
                
                .category-count {
                    color: rgba(255,255,255,0.8);
                    display: block;
                    margin-bottom: 1rem;
                }
                
                .category-button {
                    background: $primary-color;
                    color: white;
                    padding: 0.5rem 1.5rem;
                    border-radius: 25px;
                    display: inline-block;
                    font-weight: 600;
                    transition: background 0.3s ease;
                }
            }
        }
        
        &:hover .category-overlay-wrapper {
            .category-image {
                filter: brightness(0.5);
                transform: scale(1.05);
            }
            
            .category-button {
                background: $success-color;
            }
        }
    }
}

// Style 5: Minimal Design
.s_category_minimal_5 {
    .category-item-minimal-5 {
        .category-image {
            height: 80px;
            width: 80px;
            border-radius: 8px;
            margin: 0 auto;
            display: block;
            transition: transform 0.3s ease;
        }
        
        .category-title {
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        &:hover {
            .category-image {
                transform: scale(1.1);
            }
            
            .category-title {
                color: $primary-color;
            }
        }
    }
}

// Style 6: Banner Style
.s_category_banner_6 {
    .category-item-banner-6 {
        .category-banner {
            position: relative;
            height: 200px;
            border-radius: 10px;
            overflow: hidden;
            
            .category-image {
                height: 100%;
                filter: brightness(0.8);
            }
            
            .category-banner-content {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0,0,0,0.9));
                color: white;
                padding: 2rem 1.5rem 1rem;
                
                .category-title {
                    color: white;
                    font-size: 1.5rem;
                    margin-bottom: 0.5rem;
                }
                
                .category-description {
                    color: rgba(255,255,255,0.9);
                    margin-bottom: 0.5rem;
                }
                
                .category-count {
                    color: rgba(255,255,255,0.8);
                }
            }
        }
        
        &:hover .category-banner {
            .category-image {
                filter: brightness(1);
                transform: scale(1.05);
            }
        }
    }
}

// Style 7: List View
.s_category_list_7 {
    .category-item-list-7 {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
        
        .category-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .category-title {
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }
        
        .category-description {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }
        
        &:hover {
            box-shadow: 0 3px 15px rgba(0,0,0,0.15);
            
            .category-title {
                color: $primary-color;
            }
        }
    }
}

// Style 8: Masonry Layout
.s_category_masonry_8 {
    .category-item-masonry-8 {
        .category-masonry {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            
            .category-image {
                height: 200px;
                filter: brightness(0.9);
                transition: all 0.3s ease;
            }
            
            .category-masonry-content {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0,0,0,0.8));
                color: white;
                padding: 1.5rem 1rem 1rem;
                
                .category-title {
                    color: white;
                    font-size: 1.2rem;
                    margin-bottom: 0.5rem;
                }
                
                .category-count {
                    color: rgba(255,255,255,0.8);
                }
            }
        }
        
        &:hover .category-masonry {
            .category-image {
                filter: brightness(1);
                transform: scale(1.05);
            }
        }
    }
}

// Additional Interactive Features
.category-quick-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

// Hover animations for better UX
.category-item-grid-1 {
    .category-shop-btn {
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        &:hover::before {
            left: 100%;
        }
    }
}

// Loading states
.category-loading {
    .category-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

// Enhanced accessibility
.category-item-grid-1 {
    &:focus-within {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
    }

    .category-shop-btn:focus,
    .category-view-btn:focus {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
    }
}

// Responsive Design
@media (max-width: 768px) {
    .category-quick-nav {
        .btn {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
    }

    .s_category_snippet {
        .category-title {
            font-size: 1rem;
        }

        .category-description {
            font-size: 0.8rem;
        }
    }

    .s_category_grid_1 {
        .category-item-grid-1 {
            .category-image-wrapper {
                height: 150px;
            }

            .category-content {
                padding: 1rem;

                .category-actions {
                    .btn {
                        font-size: 0.75rem;
                        padding: 0.3rem 0.6rem;
                    }
                }
            }
        }
    }

    .s_category_cards_2 .category-item-cards-2 .category-card {
        height: 200px;
    }

    .s_category_circles_3 .category-item-circles-3 .category-circle {
        width: 80px;
        height: 80px;
    }

    .s_category_overlay_4 .category-item-overlay-4 .category-overlay-wrapper {
        height: 200px;

        .category-overlay-content .category-title {
            font-size: 1.3rem;
        }
    }

    .s_category_banner_6 .category-item-banner-6 .category-banner {
        height: 150px;

        .category-banner-content .category-title {
            font-size: 1.2rem;
        }
    }
}

@media (max-width: 576px) {
    .category-quick-nav {
        .btn {
            display: block;
            width: 100%;
            margin: 0 0 0.5rem 0 !important;
        }
    }

    .s_category_grid_1 {
        .category-item-grid-1 {
            .category-content {
                .category-actions {
                    .btn {
                        display: block;
                        width: 100%;
                        margin: 0.2rem 0 !important;
                    }
                }
            }
        }
    }
}
