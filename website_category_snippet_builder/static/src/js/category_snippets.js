// Category Snippet Builder JavaScript
// Simple vanilla JavaScript without modules for better compatibility

(function() {
    'use strict';

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCategorySnippets);
    } else {
        initializeCategorySnippets();
    }

    // Also initialize when page is fully loaded (for dynamic content)
    window.addEventListener('load', initializeCategorySnippets);

    function initializeCategorySnippets() {
        console.log('Initializing category snippets...');
        const categoryGrids = document.querySelectorAll('.category-grid');
        console.log('Found category grids:', categoryGrids.length);

        categoryGrids.forEach(function(grid, index) {
            console.log('Loading data for grid', index);
            loadCategoryData(grid);
        });
    }

    function loadCategoryData(gridElement) {
        console.log('Loading category data for grid:', gridElement);

        const style = gridElement.getAttribute('data-style') || 'grid_1';
        const limit = parseInt(gridElement.getAttribute('data-limit')) || 8;
        const showCount = gridElement.getAttribute('data-show-count') !== 'false';
        const showDescription = gridElement.getAttribute('data-show-description') !== 'false';

        console.log('Grid settings:', { style, limit, showCount, showDescription });

        // Show loading spinner
        showLoadingSpinner(gridElement);

        // Use XMLHttpRequest for better compatibility
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/website/category_snippet/data', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const result = JSON.parse(xhr.responseText);
                        const data = result.result || result;

                        console.log('Received data:', data);

                        if (data.success && data.categories && data.categories.length > 0) {
                            renderCategories(gridElement, data.categories, style, showCount, showDescription);
                        } else {
                            showErrorMessage(gridElement, data.error || 'No categories found');
                        }
                    } catch (error) {
                        console.error('Error parsing response:', error);
                        showErrorMessage(gridElement, 'Failed to parse response');
                    }
                } else {
                    console.error('HTTP error:', xhr.status);
                    showErrorMessage(gridElement, 'Failed to load categories (HTTP ' + xhr.status + ')');
                }
            }
        };

        const requestData = {
            jsonrpc: '2.0',
            method: 'call',
            params: {
                style: style,
                limit: limit,
                show_count: showCount,
                show_description: showDescription
            },
            id: Math.floor(Math.random() * 1000000)
        };

        console.log('Sending request:', requestData);
        xhr.send(JSON.stringify(requestData));
    }

    function showLoadingSpinner(gridElement) {
        console.log('Showing loading spinner');
        gridElement.innerHTML = '<div class="col-12 text-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading categories...</span></div></div>';
    }

    function showErrorMessage(gridElement, message) {
        console.log('Showing error message:', message);
        gridElement.innerHTML = '<div class="col-12 text-center"><div class="alert alert-warning"><h5>Unable to load categories</h5><p>' + message + '</p></div></div>';
    }

    function renderCategories(gridElement, categories, style, showCount, showDescription) {
        console.log('Rendering categories:', categories.length, 'style:', style);
        var html = '';

        for (var i = 0; i < categories.length; i++) {
            html += renderCategoryItem(categories[i], style, showCount, showDescription, i);
        }

        console.log('Generated HTML length:', html.length);
        gridElement.innerHTML = html;

        // Add hover effects and animations
        addInteractiveEffects(gridElement);
    }

    function renderCategoryItem(category, style, showCount, showDescription, index) {
        var templates = {
            'grid_1': renderGridStyle1,
            'cards_2': renderCardsStyle2,
            'circles_3': renderCirclesStyle3,
            'overlay_4': renderOverlayStyle4,
            'minimal_5': renderMinimalStyle5,
            'banner_6': renderBannerStyle6,
            'list_7': renderListStyle7,
            'masonry_8': renderMasonryStyle8
        };

        var renderFunction = templates[style] || templates['grid_1'];
        return renderFunction(category, showCount, showDescription, index);
    }

    function renderGridStyle1(category, showCount, showDescription) {
        var html = '<div class="col-lg-3 col-md-4 col-sm-6 mb-4">';
        html += '<div class="category-item-grid-1">';
        html += '<a href="' + category.shop_url + '" class="category-link">';
        html += '<div class="category-image-wrapper">';
        html += '<img src="' + category.image_url + '" alt="' + category.name + '" class="category-image"/>';
        html += '</div>';
        html += '<div class="category-content">';
        html += '<h4 class="category-title">' + category.name + '</h4>';
        if (showDescription) {
            html += '<p class="category-description">' + category.description + '</p>';
        }
        if (showCount) {
            html += '<span class="category-count">' + category.product_count + ' Products</span>';
        }
        html += '</div>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        return html;
    }

    function renderCardsStyle2(category, showCount, showDescription) {
        var html = '<div class="col-lg-4 col-md-6 mb-4">';
        html += '<div class="category-item-cards-2">';
        html += '<a href="' + category.shop_url + '" class="category-link">';
        html += '<div class="category-card">';
        html += '<img src="' + category.image_url + '" alt="' + category.name + '" class="category-image"/>';
        html += '<div class="category-overlay">';
        html += '<h4 class="category-title">' + category.name + '</h4>';
        if (showDescription) {
            html += '<p class="category-description">' + category.description + '</p>';
        }
        if (showCount) {
            html += '<span class="category-count">' + category.product_count + ' Items</span>';
        }
        html += '</div>';
        html += '</div>';
        html += '</a>';
        html += '</div>';
        html += '</div>';
        return html;
    }

    function renderCirclesStyle3(category, showCount, showDescription) {
        var html = '<div class="col-lg-3 col-md-4 col-6 mb-4">';
        html += '<div class="category-item-circles-3 text-center">';
        html += '<a href="' + category.shop_url + '" class="category-link">';
        html += '<div class="category-circle">';
        html += '<img src="' + category.image_url + '" alt="' + category.name + '" class="category-image"/>';
        html += '</div>';
        html += '<h5 class="category-title mt-3">' + category.name + '</h5>';
        if (showCount) {
            html += '<span class="category-count">' + category.product_count + ' Products</span>';
        }
        html += '</a>';
        html += '</div>';
        html += '</div>';
        return html;
    }

    // Simplified rendering for other styles - they all use grid_1 as fallback
    function renderOverlayStyle4(category, showCount, showDescription, index) {
        return renderGridStyle1(category, showCount, showDescription);
    }

    function renderMinimalStyle5(category, showCount, showDescription, index) {
        return renderGridStyle1(category, showCount, showDescription);
    }

    function renderBannerStyle6(category, showCount, showDescription, index) {
        return renderGridStyle1(category, showCount, showDescription);
    }

    function renderListStyle7(category, showCount, showDescription, index) {
        return renderGridStyle1(category, showCount, showDescription);
    }

    function renderMasonryStyle8(category, showCount, showDescription, index) {
        return renderGridStyle1(category, showCount, showDescription);
    }



    function addInteractiveEffects(gridElement) {
        // Add smooth scroll to top when clicking categories
        var categoryLinks = gridElement.querySelectorAll('.category-link');

        for (var i = 0; i < categoryLinks.length; i++) {
            categoryLinks[i].addEventListener('click', function(e) {
                // Add a subtle loading effect
                this.style.opacity = '0.7';
                var self = this;
                setTimeout(function() {
                    self.style.opacity = '1';
                }, 200);
            });
        }
    }

    // Export for use in snippet options
    window.CategorySnippetBuilder = {
        initializeCategorySnippets: initializeCategorySnippets,
        loadCategoryData: loadCategoryData
    };

})();
