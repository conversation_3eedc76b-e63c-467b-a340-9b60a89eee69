/** @odoo-module **/

import options from "@web_editor/js/editor/snippets.options";

options.registry.CategorySnippetOptions = options.Class.extend({

    /**
     * @override
     */
    start: function () {
        var self = this;
        return this._super.apply(this, arguments).then(function () {
            // Initialize categories when snippet is loaded
            self._reloadCategories();
        });
    },

    /**
     * Handle option changes
     */
    selectDataAttribute: function (previewMode, value, $opt) {
        var self = this;
        return this._super.apply(this, arguments).then(function () {
            // Reload categories when options change
            self._reloadCategories();
        });
    },

    /**
     * Handle checkbox changes
     */
    selectClass: function (previewMode, value, $opt) {
        var self = this;
        return this._super.apply(this, arguments).then(function () {
            // Reload categories when options change
            self._reloadCategories();
        });
    },

    /**
     * Reload categories with current settings
     */
    _reloadCategories: function () {
        var $categoryGrid = this.$target.find('.category-grid');

        if ($categoryGrid.length && window.CategorySnippetBuilder) {
            window.CategorySnippetBuilder.loadCategoryData($categoryGrid[0]);
        }
    },

    /**
     * @override
     */
    onBuilt: function () {
        this._super.apply(this, arguments);

        // Initialize categories when snippet is first added
        var self = this;
        setTimeout(function() {
            self._reloadCategories();
        }, 100);
    },

    /**
     * @override
     */
    onClone: function () {
        this._super.apply(this, arguments);

        // Initialize categories when snippet is cloned
        var self = this;
        setTimeout(function() {
            self._reloadCategories();
        }, 100);
    },

    /**
     * @override
     */
    onMove: function () {
        this._super.apply(this, arguments);

        // Reinitialize categories when snippet is moved
        var self = this;
        setTimeout(function() {
            self._reloadCategories();
        }, 100);
    }
});
