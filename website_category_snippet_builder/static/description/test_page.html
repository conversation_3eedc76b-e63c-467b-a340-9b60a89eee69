<!DOCTYPE html>
<html>
<head>
    <title>Category Snippet Builder Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 40px 0; padding: 20px; border: 1px solid #ddd; }
        .category-grid { display: flex; flex-wrap: wrap; gap: 20px; }
        .category-item { border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>Category Snippet Builder Test Page</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct API Call</h2>
        <button onclick="testAPI()">Test Category API</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Category Grid</h2>
        <div class="category-grid" data-style="grid_1" data-limit="4" data-show-count="true" data-show-description="true">
            <div>Loading categories...</div>
        </div>
        <button onclick="manualLoad()">Manual Load</button>
    </div>
    
    <script>
        async function testAPI() {
            try {
                const response = await fetch('/website/category_snippet/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {
                            style: 'grid_1',
                            limit: 4,
                            show_count: true,
                            show_description: true
                        },
                        id: 1
                    })
                });
                
                const result = await response.json();
                document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('api-result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        // Load category snippet JavaScript
        async function loadCategoryData(gridElement) {
            try {
                const style = gridElement.getAttribute('data-style') || 'grid_1';
                const limit = parseInt(gridElement.getAttribute('data-limit')) || 8;
                const showCount = gridElement.getAttribute('data-show-count') !== 'false';
                const showDescription = gridElement.getAttribute('data-show-description') !== 'false';
                
                const response = await fetch('/website/category_snippet/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {
                            style: style,
                            limit: limit,
                            show_count: showCount,
                            show_description: showDescription
                        },
                        id: Math.floor(Math.random() * 1000000)
                    })
                });
                
                const result = await response.json();
                const data = result.result || result;
                
                if (data.success && data.categories && data.categories.length > 0) {
                    let html = '';
                    data.categories.forEach(category => {
                        html += `
                            <div class="category-item">
                                <h4>${category.name}</h4>
                                ${showDescription ? `<p>${category.description}</p>` : ''}
                                ${showCount ? `<span>Products: ${category.product_count}</span>` : ''}
                                <br><a href="${category.shop_url}">Shop Now</a>
                            </div>
                        `;
                    });
                    gridElement.innerHTML = html;
                } else {
                    gridElement.innerHTML = '<div>No categories found</div>';
                }
                
            } catch (error) {
                console.error('Error loading category data:', error);
                gridElement.innerHTML = '<div>Failed to load categories</div>';
            }
        }
        
        function manualLoad() {
            const categoryGrids = document.querySelectorAll('.category-grid');
            categoryGrids.forEach(grid => {
                loadCategoryData(grid);
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const categoryGrids = document.querySelectorAll('.category-grid');
            categoryGrids.forEach(grid => {
                loadCategoryData(grid);
            });
        });
    </script>
</body>
</html>
