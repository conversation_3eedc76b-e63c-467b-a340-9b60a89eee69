#!/usr/bin/env python3
"""
Automated test script for bi_website_hide_price module
This script will test the module installation and basic functionality
"""

import requests
import json
import time
import sys

# Odoo connection settings
ODOO_URL = "http://localhost:8069"
DB_NAME = "odoo"
USERNAME = "admin"
PASSWORD = "admin"

class OdooTester:
    def __init__(self):
        self.session = requests.Session()
        self.uid = None
        
    def authenticate(self):
        """Authenticate with Odoo"""
        print("🔐 Authenticating with Odoo...")
        
        # Get session info
        response = self.session.post(f"{ODOO_URL}/web/session/authenticate", json={
            "jsonrpc": "2.0",
            "method": "call",
            "params": {
                "db": DB_NAME,
                "login": USERNAME,
                "password": PASSWORD
            },
            "id": 1
        })
        
        if response.status_code == 200:
            result = response.json()
            if result.get("result") and result["result"].get("uid"):
                self.uid = result["result"]["uid"]
                print(f"✅ Authenticated successfully (UID: {self.uid})")
                return True
        
        print("❌ Authentication failed")
        return False
    
    def call_odoo(self, model, method, args=None, kwargs=None):
        """Make RPC call to Odoo"""
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}
            
        response = self.session.post(f"{ODOO_URL}/web/dataset/call_kw", json={
            "jsonrpc": "2.0",
            "method": "call",
            "params": {
                "model": model,
                "method": method,
                "args": args,
                "kwargs": kwargs
            },
            "id": 1
        })
        
        if response.status_code == 200:
            result = response.json()
            if "result" in result:
                return result["result"]
            elif "error" in result:
                print(f"❌ Odoo Error: {result['error']}")
                return None
        
        print(f"❌ HTTP Error: {response.status_code}")
        return None
    
    def test_module_exists(self):
        """Test if the module exists in the system"""
        print("\n📦 Checking if module exists...")
        
        modules = self.call_odoo("ir.module.module", "search_read", [
            [("name", "=", "bi_website_hide_price")]
        ], {"fields": ["name", "state", "summary"]})
        
        if modules:
            module = modules[0]
            print(f"✅ Module found: {module['name']}")
            print(f"   State: {module['state']}")
            print(f"   Summary: {module['summary']}")
            return module
        else:
            print("❌ Module not found in system")
            return None
    
    def install_module(self):
        """Install the module"""
        print("\n🔧 Installing module...")
        
        # First update module list
        print("   Updating module list...")
        self.call_odoo("ir.module.module", "update_list")
        
        # Find the module
        module = self.test_module_exists()
        if not module:
            return False
        
        if module["state"] == "installed":
            print("✅ Module already installed")
            return True
        
        # Install the module
        print("   Installing module...")
        try:
            self.call_odoo("ir.module.module", "button_immediate_install", [module["id"]])
            print("✅ Module installation initiated")
            
            # Wait a bit for installation
            time.sleep(5)
            
            # Check if installed
            updated_module = self.call_odoo("ir.module.module", "read", [module["id"]], {"fields": ["state"]})
            if updated_module and updated_module[0]["state"] == "installed":
                print("✅ Module installed successfully")
                return True
            else:
                print("⚠️  Module installation may still be in progress")
                return True
                
        except Exception as e:
            print(f"❌ Installation failed: {e}")
            return False
    
    def test_product_fields(self):
        """Test if product fields were added"""
        print("\n🏷️  Testing product fields...")
        
        # Get field info
        fields = self.call_odoo("product.template", "fields_get", [], {
            "attributes": ["string", "type"]
        })
        
        if fields:
            expected_fields = ["website_hide_price", "website_hide_price_message"]
            found_fields = []
            
            for field_name in expected_fields:
                if field_name in fields:
                    found_fields.append(field_name)
                    print(f"✅ Field '{field_name}' found")
                else:
                    print(f"❌ Field '{field_name}' missing")
            
            return len(found_fields) == len(expected_fields)
        
        return False
    
    def test_config_settings(self):
        """Test if configuration settings were added"""
        print("\n⚙️  Testing configuration settings...")
        
        # Get settings fields
        fields = self.call_odoo("res.config.settings", "fields_get", [], {
            "attributes": ["string", "type"]
        })
        
        if fields:
            expected_fields = ["website_hide_price_option", "website_hide_price_message"]
            found_fields = []
            
            for field_name in expected_fields:
                if field_name in fields:
                    found_fields.append(field_name)
                    print(f"✅ Setting '{field_name}' found")
                else:
                    print(f"❌ Setting '{field_name}' missing")
            
            return len(found_fields) == len(expected_fields)
        
        return False
    
    def run_tests(self):
        """Run all tests"""
        print("="*60)
        print("  AUTOMATED BI_WEBSITE_HIDE_PRICE MODULE TEST")
        print("="*60)
        
        if not self.authenticate():
            return False
        
        # Test module existence and installation
        if not self.install_module():
            return False
        
        # Test fields were added
        product_fields_ok = self.test_product_fields()
        config_settings_ok = self.test_config_settings()
        
        print("\n" + "="*60)
        print("  TEST RESULTS")
        print("="*60)
        
        if product_fields_ok and config_settings_ok:
            print("✅ ALL TESTS PASSED!")
            print("   The module is installed and working correctly.")
            print("   You can now proceed with manual testing.")
            return True
        else:
            print("❌ SOME TESTS FAILED!")
            print("   Please check the installation and try again.")
            return False

def main():
    tester = OdooTester()
    success = tester.run_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
