<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Website Configuration Settings -->
        <record id="res_config_settings_view_form_inherit" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.hide.price</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@class='app_settings_block']" position="inside">
                    <h2>Website Price Visibility</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="website_hide_price_option" widget="radio"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="website_hide_price_option"/>
                                <div class="text-muted">
                                    Configure when to hide product prices on your website
                                </div>
                                <div class="content-group mt16"
                                     invisible="website_hide_price_option == 'none'">
                                    <div class="row">
                                        <label for="website_hide_price_message" class="col-3 o_light_label"/>
                                        <field name="website_hide_price_message" class="col-9"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
