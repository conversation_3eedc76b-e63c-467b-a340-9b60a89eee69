# Shop Product Price Visibility

This module allows you to hide product prices on your website based on different configurations.

## Features

- **Hide prices for all users**: Completely hide prices from all website visitors
- **Hide prices for guest users only**: Show prices only to logged-in users
- **Product-level control**: Individual checkbox on each product to enable/disable price hiding
- **Custom messages**: Display custom messages instead of prices
- **Contact Us button**: Replace "Add to Cart" with "Contact Us" button when prices are hidden
- **Works everywhere**: Product lists, detail pages, quick view modals

## Configuration

1. Go to **Website > Configuration > Settings**
2. Find the "Website Price Visibility" section
3. Choose your preferred option:
   - **Show Prices (Default)**: Normal behavior, show all prices
   - **Hide Price for All Users**: Hide prices from everyone
   - **Hide Price Only for Guest Users**: Hide prices only from non-logged-in users
4. Set a default custom message to display instead of prices

## Product Configuration

1. Go to any product in **Sales > Products > Products**
2. In the **Sales** tab, find the "Website Price Visibility" section
3. Check **Website Hide Price** to enable price hiding for this specific product
4. Enter a **Custom Message** to display instead of the price (optional)

## How it Works

- When a product has price hiding enabled AND the website configuration allows it, prices will be hidden
- Instead of prices, your custom message will be displayed
- The "Add to Cart" button is replaced with a "Contact Us" button
- This works on:
  - Product listing pages
  - Product detail pages  
  - Quick view modals
  - Recently viewed products

## Use Cases

Perfect for:
- **Brand showcase websites**: Display products without selling directly
- **B2B lead generation**: Encourage contact for custom pricing
- **Quote-based selling**: Force customers to request quotes
- **Catalog browsing**: Let customers browse without seeing prices

## Technical Details

- Compatible with Odoo 18.0 Community Edition
- Depends on: website, website_sale, account, mail
- Uses configuration parameters for global settings
- Product-level fields for individual control
- Template inheritance for frontend modifications

## Author

Created by **vear** based on the original bi_website_hide_price module functionality.
