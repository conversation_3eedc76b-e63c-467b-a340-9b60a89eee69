# -*- coding: utf-8 -*-
{
    'name': 'Shop Product Price Visibility',
    'version': '********.0',
    'category': 'eCommerce',
    'summary': 'Hide product price on website for all users or guest users only',
    'description': '''
        Shop Product Price Visibility
        ============================

        This module helps users to hide product price on the website.

        Features:
        ---------
        * Hide product price for all users
        * Hide product price only for guest users
        * Product-level control with checkbox
        * Custom message instead of price
        * Contact Us button instead of Add to Cart
        * Works on product lists and detail pages
        * Easy configuration through website settings

        Perfect for:
        -----------
        * Brand showcase websites
        * B2B lead generation
        * Quote-based selling
        * Catalog browsing without prices
    ''',
    'author': 'vear',
    'website': 'https://www.browseinfo.com',
    'license': 'LGPL-3',
    'depends': [
        'website',
        'website_sale',
        'account',
        'mail',
    ],
    'data': [
        'views/product_template_views.xml',
        # 'views/website_config_settings_views.xml',  # Temporarily disabled
        'views/website_templates.xml',  # Re-enabled with proper templates
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
