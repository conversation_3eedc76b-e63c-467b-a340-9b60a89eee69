# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    website_hide_price = fields.Boolean(
        string='Website Hide Price',
        help='Hide product price on website'
    )

    website_hide_price_message = fields.Text(
        string='Custom Message',
        help='Custom message to display instead of price when price is hidden',
        default='Available in store'
    )

    def _should_hide_price(self):
        """Check if price should be hidden for this product"""
        if not self.website_hide_price:
            return False

        # For now, always hide price if the checkbox is checked
        # Later we can add website configuration
        return True

    def _get_hide_price_message(self):
        """Get the message to display instead of price"""
        if self.website_hide_price_message:
            return self.website_hide_price_message

        # Get default message from configuration
        default_message = self.env['ir.config_parameter'].sudo().get_param(
            'bi_website_hide_price.default_message', 'Contact us for pricing information'
        )
        return default_message


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def _should_hide_price(self):
        """Check if price should be hidden for this product variant"""
        return self.product_tmpl_id._should_hide_price()

    def _get_hide_price_message(self):
        """Get the message to display instead of price"""
        return self.product_tmpl_id._get_hide_price_message()
