# -*- coding: utf-8 -*-

from odoo import models, fields


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    website_hide_price_option = fields.Selection([
        ('none', 'Show Prices (Default)'),
        ('all', 'Hide Price for All Users'),
        ('guest', 'Hide Price Only for Guest Users'),
    ], string='Website Price Visibility',
       default='none',
       config_parameter='bi_website_hide_price.hide_price_option',
       help='Configure price visibility on website')

    website_hide_price_message = fields.Text(
        string='Default Custom Message',
        default='Available in store',
        config_parameter='bi_website_hide_price.default_message',
        help='Default message to show instead of price'
    )