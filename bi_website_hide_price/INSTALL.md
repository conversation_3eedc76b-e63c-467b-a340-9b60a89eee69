# Installation Guide - Shop Product Price Visibility

## Prerequisites

- Odoo 18.0 Community Edition
- Website and eCommerce modules installed
- Administrator access to Odoo

## Installation Steps

### 1. Copy Module to Addons Directory

Copy the `bi_website_hide_price` folder to your Odoo addons directory:

```bash
# If using Docker
cp -r bi_website_hide_price /path/to/odoo/addons/

# If using source installation  
cp -r bi_website_hide_price /path/to/odoo/addons/
```

### 2. Update Module List

1. Go to **Apps** in Odoo
2. Click **Update Apps List** (you may need to enable Developer Mode)
3. Search for "Shop Product Price Visibility" or "vear"
4. Click **Install**

### 3. Configure the Module

#### Global Configuration:
1. Go to **Website > Configuration > Settings**
2. Find "Website Price Visibility" section
3. Choose your preferred option:
   - **Show Prices (Default)**: Normal behavior
   - **Hide Price for All Users**: Hide from everyone
   - **Hide Price Only for Guest Users**: Hide only from non-logged users
4. Set a default message to show instead of prices
5. Click **Save**

#### Product-Level Configuration:
1. Go to **Sales > Products > Products**
2. Open any product
3. Go to **Sales** tab
4. Find "Website Price Visibility" section
5. Check **Website Hide Price** to enable for this product
6. Enter a **Custom Message** (optional)
7. Click **Save**

## Testing

### Test Price Hiding:
1. Enable price hiding for a test product
2. Visit your website
3. Navigate to the product page
4. Verify that:
   - Price is hidden
   - Custom message is displayed
   - "Contact Us" button appears instead of "Add to Cart"

### Test Guest vs Logged Users:
1. Set configuration to "Hide Price Only for Guest Users"
2. Test as guest user (logged out)
3. Test as logged-in user
4. Verify different behavior

## Troubleshooting

### Module Not Appearing:
- Ensure the module is in the correct addons directory
- Check that all required dependencies are installed
- Update the apps list
- Check Odoo logs for any errors

### Price Still Showing:
- Verify the product has "Website Hide Price" checked
- Check the global configuration setting
- Clear browser cache
- Check if you're testing as the correct user type (guest vs logged-in)

### Template Errors:
- Check Odoo logs for template inheritance errors
- Ensure website_sale module is properly installed
- Restart Odoo server after installation

## Uninstallation

1. Go to **Apps**
2. Search for "Shop Product Price Visibility"
3. Click **Uninstall**
4. Confirm the uninstallation

**Note**: Uninstalling will remove all configuration and product-level settings.

## Support

For issues or questions:
- Check the README.md file for detailed documentation
- Review Odoo logs for error messages
- Ensure all dependencies are properly installed
