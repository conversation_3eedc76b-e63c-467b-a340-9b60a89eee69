version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data/pgdata
    ports:
      - "5433:5432"
    restart: unless-stopped

  odoo:
    image: odoo:18.0
    depends_on:
      - db
    ports:
      - "8069:8069"
    environment:
      HOST: db
      USER: odoo
      PASSWORD: odoo
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./website_product_attachments:/mnt/extra-addons/website_product_attachments
      - ./website_product_extra_info:/mnt/extra-addons/website_product_extra_info
      - ./topppro_theme:/mnt/extra-addons/topppro_theme
      - ./theme_eshop:/mnt/extra-addons/theme_eshop
      - ./website_category_snippet_builder:/mnt/extra-addons/website_category_snippet_builder
      - ./bi_website_hide_price:/mnt/extra-addons/bi_website_hide_price
    restart: unless-stopped
    command: >
      --dev=reload,qweb,werkzeug,xml
      --log-level=info
      --workers=0
      --database=odoo
      --db_host=db
      --db_user=odoo
      --db_password=odoo
      --init=base

volumes:
  odoo-web-data:
  odoo-db-data:
