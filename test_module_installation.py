#!/usr/bin/env python3
"""
Test script to guide manual testing of bi_website_hide_price module
"""

def print_header(title):
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step_num, description):
    print(f"\n📋 STEP {step_num}: {description}")
    print("-" * 50)

def wait_for_user():
    input("\n✅ Press Enter when completed...")

def main():
    print_header("BI WEBSITE HIDE PRICE - TESTING GUIDE")
    
    print("""
🎯 This guide will help you test the bi_website_hide_price module
📍 Odoo should be running at: http://localhost:8069
🔧 Make sure you're logged in as Administrator
    """)
    
    # Step 1: Install Module
    print_step(1, "INSTALL THE MODULE")
    print("""
1. Go to Apps menu in Odoo
2. Click 'Update Apps List' (enable Developer Mode if needed)
3. Search for 'Shop Product Price Visibility' or 'vear'
4. Click 'Install' on the module
5. Wait for installation to complete
    """)
    wait_for_user()
    
    # Step 2: Configure Website Settings
    print_step(2, "CONFIGURE WEBSITE SETTINGS")
    print("""
1. Go to Website > Configuration > Settings
2. Scroll down to find 'Website Price Visibility' section
3. Try each option:
   - 'Show Prices (Default)' - Normal behavior
   - 'Hide Price for All Users' - Hide from everyone
   - 'Hide Price Only for Guest Users' - Hide only from non-logged users
4. Set a default message like 'Contact us for pricing'
5. Click 'Save'
    """)
    wait_for_user()
    
    # Step 3: Configure a Test Product
    print_step(3, "CONFIGURE A TEST PRODUCT")
    print("""
1. Go to Sales > Products > Products
2. Open any existing product (or create a new one)
3. Go to the 'Sales' tab
4. Find 'Website Price Visibility' section
5. Check the 'Website Hide Price' checkbox
6. Enter a custom message like 'Special pricing available - contact us!'
7. Click 'Save'
8. Make sure the product is published on website
    """)
    wait_for_user()
    
    # Step 4: Test on Website - All Users
    print_step(4, "TEST PRICE HIDING - ALL USERS")
    print("""
1. Set Website Settings to 'Hide Price for All Users'
2. Go to your website (click 'Go to Website' button)
3. Navigate to Shop/Products
4. Find your test product
5. Verify:
   ✓ Price is hidden
   ✓ Custom message is displayed
   ✓ 'Contact Us' button appears instead of 'Add to Cart'
6. Click on the product to view details
7. Verify same behavior on product detail page
    """)
    wait_for_user()
    
    # Step 5: Test Guest vs Logged Users
    print_step(5, "TEST GUEST VS LOGGED USERS")
    print("""
1. Set Website Settings to 'Hide Price Only for Guest Users'
2. While logged in as admin, visit the product page
3. Verify: Price should be VISIBLE (you're logged in)
4. Open an incognito/private browser window
5. Go to the same product page
6. Verify: Price should be HIDDEN (you're a guest)
7. Test 'Contact Us' button works in guest mode
    """)
    wait_for_user()
    
    # Step 6: Test Product-Level Control
    print_step(6, "TEST PRODUCT-LEVEL CONTROL")
    print("""
1. Create/edit another product
2. Leave 'Website Hide Price' UNCHECKED
3. Set Website Settings to 'Hide Price for All Users'
4. Visit both products on website:
   - Product with checkbox: Price should be HIDDEN
   - Product without checkbox: Price should be VISIBLE
5. This tests individual product control
    """)
    wait_for_user()
    
    # Step 7: Test Different Areas
    print_step(7, "TEST IN DIFFERENT WEBSITE AREAS")
    print("""
Test the price hiding in these areas:
1. Product listing page (/shop)
2. Product detail page
3. Quick view modal (if available)
4. Search results
5. Category pages
6. Recently viewed products

Verify consistent behavior across all areas.
    """)
    wait_for_user()
    
    # Step 8: Test Edge Cases
    print_step(8, "TEST EDGE CASES")
    print("""
1. Test with products that have variants
2. Test with products on sale/discounted
3. Test with products that have no price set
4. Test switching between different website settings
5. Test with different user roles (create a portal user)
    """)
    wait_for_user()
    
    print_header("TESTING COMPLETE!")
    print("""
🎉 Congratulations! You've completed the testing.

📝 EXPECTED RESULTS SUMMARY:
✓ Module installs without errors
✓ Website settings appear and work
✓ Product-level checkboxes appear and work  
✓ Prices hide/show based on configuration
✓ Custom messages display correctly
✓ 'Contact Us' buttons replace 'Add to Cart'
✓ Different behavior for guests vs logged users
✓ Consistent behavior across all website areas

🐛 If you found any issues:
1. Check Odoo logs for errors
2. Verify all dependencies are installed
3. Clear browser cache and try again
4. Check that products are published on website

✅ The module is working correctly if all tests passed!
    """)

if __name__ == "__main__":
    main()
