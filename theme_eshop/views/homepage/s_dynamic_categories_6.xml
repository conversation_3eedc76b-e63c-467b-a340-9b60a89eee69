<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="s_dynamic_categories_6" name="Dynamic Categories Grid (6 Items)">
        <section class="s_dynamic_categories pt64 pb64 o_colored_level" data-vcss="001" data-snippet="s_dynamic_categories_6" data-name="Dynamic Categories (6)" style="position: relative;">
            <div class="container">
                <!-- Header Section -->
                <div class="row mb-5">
                    <div class="col-12 text-center">
                        <h2 class="h2 mb-3">Shop by Category</h2>
                        <p class="lead text-muted">Discover our product categories - dynamically loaded from your system</p>
                    </div>
                </div>
                
                <!-- Dynamic Category Grid - 6 Categories -->
                <div class="dynamic-category-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 20px; max-width: 800px; margin: 0 auto;">
                    
                    <!-- Server-side rendered categories - Limited to 6 -->
                    <t t-set="categories" t-value="request.env['product.public.category'].sudo().search([('parent_id', '=', False)], limit=6, order='sequence, name')"/>
                    <t t-if="categories and len(categories) > 0">
                        <t t-foreach="categories" t-as="category">
                            <div class="dynamic-category-item">
                                <a t-attf-href="/shop?category=#{category.id}" class="dynamic-category-link">
                                    <!-- Category Image -->
                                    <div class="dynamic-category-image-wrapper">
                                        <img t-if="category.image_1920" 
                                             t-attf-src="/web/image/product.public.category/#{category.id}/image_1920" 
                                             t-attf-alt="#{category.name}" 
                                             class="dynamic-category-image"/>
                                        <img t-else="" 
                                             t-attf-src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&amp;h=200&amp;fit=crop&amp;q=#{category.id % 10}" 
                                             t-attf-alt="#{category.name}" 
                                             class="dynamic-category-image"/>
                                        
                                        <!-- Category Overlay -->
                                        <div class="dynamic-category-overlay">
                                            <div class="dynamic-category-info">
                                                <span class="dynamic-category-count">
                                                    <i class="fa fa-cube me-1"></i>
                                                    <t t-esc="len(category.product_tmpl_ids)"/> Items
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Category Title -->
                                    <div class="dynamic-category-title">
                                        <span t-esc="category.name"/>
                                    </div>
                                </a>
                            </div>
                        </t>
                    </t>
                    
                    <!-- Fallback static categories if no dynamic categories found -->
                    <t t-else="">
                        <!-- Electronics -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=electronics" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Electronics" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>45 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Electronics</div>
                            </a>
                        </div>
                        
                        <!-- Clothing -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=clothing" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Clothing" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>78 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Clothing</div>
                            </a>
                        </div>
                        
                        <!-- Home & Garden -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=home" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Home &amp; Garden" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>32 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Home &amp; Garden</div>
                            </a>
                        </div>
                        
                        <!-- Sports -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=sports" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Sports" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>56 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Sports</div>
                            </a>
                        </div>
                        
                        <!-- Books -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=books" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Books" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>23 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Books</div>
                            </a>
                        </div>
                        
                        <!-- Beauty -->
                        <div class="dynamic-category-item">
                            <a href="/shop?search=beauty" class="dynamic-category-link">
                                <div class="dynamic-category-image-wrapper">
                                    <img src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&amp;h=200&amp;fit=crop" 
                                         alt="Beauty" 
                                         class="dynamic-category-image"/>
                                    <div class="dynamic-category-overlay">
                                        <div class="dynamic-category-info">
                                            <span class="dynamic-category-count">
                                                <i class="fa fa-cube me-1"></i>41 Items
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dynamic-category-title">Beauty</div>
                            </a>
                        </div>
                    </t>
                </div>
                
                <!-- View All Categories Button -->
                <div class="row mt-5">
                    <div class="col-12 text-center">
                        <a href="/shop" class="btn btn-primary btn-lg">
                            <i class="fa fa-th-large me-2"></i>View All Products
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </template>
</odoo>
