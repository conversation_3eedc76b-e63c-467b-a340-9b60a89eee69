<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="s_dynamic_snippet_two" name="Dynamic Snippet Two">
        <section data-snippet="s_dynamic_snippet_products" class="s_dynamic_snippet_two s_dynamic_snippet_products s_dynamic s_dynamic_empty pt32 pb32 o_colored_level s_product_product_horizontal_card o_dynamic_snippet_empty" data-custom-template-data="{}" data-name="Products" data-product-category-id="all" data-show-variants="true" data-number-of-records="16" data-filter-id="1" data-template-key="website_sale.dynamic_filter_template_product_product_horizontal_card" data-carousel-interval="5000" data-number-of-elements="3" data-number-of-elements-small-devices="1" data-extra-classes="o_carousel_multiple_rows" data-row-per-slide="2" data-arrow-position="bottom">
            <div class="container">
                <div class="row s_nb_column_fixed">
                    <section class="s_dynamic_snippet_title oe_unremovable oe_unmovable d-flex flex-column flex-md-row mb-lg-0 pb-3 pb-md-0 o_colored_level justify-content-between">
                        <div>
                            <h4 class="o_default_snippet_text">Our latest content</h4>
                            <p class="lead" style="text-align: center;">Check out what's new in our company !</p>
                        </div>
                        <div>
                            <a title="See All" class="s_dynamic_snippet_main_page_url o_default_snippet_text" href="/shop">See all <i class="fa fa-long-arrow-right ms-2"/></a>
                        </div>
                    </section>
                    <section class="s_dynamic_snippet_content oe_unremovable oe_unmovable o_not_editable col o_colored_level">
                        <div class="css_non_editable_mode_hidden">
                            <div class="missing_option_warning alert alert-info fade show d-none d-print-none rounded-0 o_default_snippet_text">
                                Your Dynamic Snippet will be displayed here... This message is displayed because you did not provide both a filter and a template to use.<br/>
                            </div>
                        </div>
                        <div class="dynamic_snippet_template"/>
                    </section>
                </div>
            </div>
        </section>
    </template>
</odoo>