/* Hide eCommerce Prices for Brand Profile */

/* Hide all price-related elements */
.oe_currency_value,
.oe_price,
.product_price,
.js_product_price,
.o_base_unit_price,
.o_base_unit_price_wrapper,
.price,
.product-price,
.o_wsale_product_price,
.o_wsale_product_list_price,
.o_wsale_product_price_wrapper {
    display: none !important;
}

/* Hide price in product cards */
.o_wsale_product_card .product_price,
.o_wsale_product_card .o_wsale_product_price,
.oe_product_cart .product_price,
.oe_product .product_price {
    display: none !important;
}

/* Hide price in product list */
.o_wsale_products_grid_table_wrapper .product_price,
.o_wsale_products_grid_table_wrapper .o_wsale_product_price,
.o_wsale_products_item .product_price,
.o_wsale_products_item .o_wsale_product_price {
    display: none !important;
}

/* Hide price in product detail page */
.o_wsale_product_page .product_price,
.o_wsale_product_page .o_wsale_product_price,
.o_wsale_product_page .js_product_price,
.o_wsale_product_page .o_base_unit_price_wrapper {
    display: none !important;
}

/* Hide cart and checkout related elements */
.o_wsale_cart_button,
.o_wsale_product_btn,
.btn-primary[href*="shop/cart"],
.a-submit[name="add_qty"],
.js_add_cart_variants,
.o_add_cart,
.o_add_cart_json {
    display: none !important;
}

/* Hide quantity selectors */
.css_quantity,
.js_quantity,
.o_wsale_quantity_wrapper,
.input-group.css_quantity {
    display: none !important;
}

/* Hide "Add to Cart" buttons */
button[name="add_qty"],
.btn[name="add_qty"],
.o_add_cart_json,
.js_add_cart_variants,
.o_wsale_product_btn {
    display: none !important;
}

/* Hide shopping cart icon and counter */
.o_wsale_my_cart,
.my_cart_quantity,
.o_cart_quantity,
.fa-shopping-cart,
.o_wsale_navbar_cart {
    display: none !important;
}

/* Hide checkout and payment related */
.o_wsale_checkout,
.o_payment_form,
.o_wsale_payment,
.oe_cart {
    display: none !important;
}

/* Hide price filters in shop */
.o_wsale_products_searchbar_price,
.o_wsale_price_filter,
.price-filter,
.o_wsale_products_searchbar .o_wsale_price_filter {
    display: none !important;
}

/* Hide currency and monetary symbols */
.oe_currency,
.o_currency,
span[t-field*="currency"],
span[t-esc*="currency"] {
    display: none !important;
}

/* Hide sale badges and discounts */
.badge.bg-danger,
.o_ribbon_left,
.o_ribbon_right,
.discount,
.sale-badge,
.o_wsale_product_ribbon {
    display: none !important;
}

/* Hide wishlist price-related elements */
.o_wsale_wishlist_price,
.wishlist .product_price {
    display: none !important;
}

/* Hide comparison price elements */
.o_product_comparison_table .product_price,
.o_comparelist .product_price {
    display: none !important;
}

/* Replace "Add to Cart" with "View Details" or "Learn More" */
.o_wsale_product_btn::after {
    content: "Learn More";
    display: inline-block;
}

/* Style adjustments for brand profile */
.o_wsale_product_card {
    .card-body {
        padding-bottom: 1rem;
    }
    
    .product-title {
        margin-bottom: 0.5rem;
    }
}

/* Hide specific eCommerce menu items */
.o_main_navbar a[href="/shop/cart"],
.o_main_navbar a[href="/shop/checkout"],
.o_main_navbar .o_wsale_my_cart {
    display: none !important;
}

/* Hide product variants price impact */
.variant_price_extra,
.js_variant_change .badge,
.o_variant_pills .badge {
    display: none !important;
}

/* Hide shipping and delivery price info */
.o_wsale_delivery_price,
.delivery-price,
.shipping-price {
    display: none !important;
}

/* Hide tax information */
.o_wsale_tax_info,
.tax-info,
.o_tax_message {
    display: none !important;
}

/* Brand profile enhancements */
.brand-profile-mode {
    .o_wsale_product_card {
        transition: transform 0.3s ease;
        
        &:hover {
            transform: translateY(-5px);
        }
    }
    
    .product-title {
        font-weight: 600;
        color: #333;
    }
    
    .product-description {
        color: #666;
        font-size: 0.9rem;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .o_wsale_product_card .card-body {
        padding: 0.75rem;
    }
    
    .product-title {
        font-size: 1rem;
    }
}

/* Print styles - hide prices in print too */
@media print {
    .oe_currency_value,
    .oe_price,
    .product_price,
    .o_wsale_product_price,
    .o_wsale_cart_button,
    .o_add_cart {
        display: none !important;
    }
}
