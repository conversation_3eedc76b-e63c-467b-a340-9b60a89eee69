/* TopPro Exact Category Layout Styles for Theme eShop */

// TopPro Color Variables
$topppro-primary: #1a1a1a;
$topppro-secondary: #ff6b35;
$topppro-light-gray: #f8f9fa;
$topppro-dark-gray: #6c757d;
$topppro-white: #ffffff;

// Category Grid Section - Exact TopPro Layout
.s_topppro_categories {
    background: $topppro-white;
    padding: 40px 0;

    .container {
        max-width: 1200px;
    }
}

// TopPro Category Grid - Exact Layout
.topppro-category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0;
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid #ddd;
}

// TopPro Category Items - Simple Layout
.topppro-category-item {
    border: 1px solid #ddd;
    border-right: none;
    border-bottom: none;

    &:nth-child(4n) {
        border-right: 1px solid #ddd;
    }

    &:nth-child(n+5) {
        border-bottom: 1px solid #ddd;
    }
}

.topppro-category-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;

    &:hover {
        text-decoration: none;
        color: inherit;

        .topppro-category-image {
            opacity: 0.8;
        }

        .topppro-category-title {
            color: $topppro-secondary;
        }
    }
}

// TopPro Category Image - Simple Style
.topppro-category-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
    transition: opacity 0.3s ease;
}

// TopPro Category Title - Simple Style
.topppro-category-title {
    padding: 10px;
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    color: $topppro-primary;
    background: $topppro-white;
    border-top: 1px solid #ddd;
    transition: color 0.3s ease;
    margin: 0;
}

// Responsive Design for TopPro Layout
@media (max-width: 768px) {
    .topppro-category-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 400px;
    }

    .topppro-category-item {
        &:nth-child(4n) {
            border-right: none;
        }

        &:nth-child(2n) {
            border-right: 1px solid #ddd;
        }

        &:nth-child(n+3) {
            border-bottom: 1px solid #ddd;
        }
    }

    .topppro-category-image {
        height: 100px;
    }

    .topppro-category-title {
        font-size: 12px;
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .topppro-category-grid {
        grid-template-columns: repeat(1, 1fr);
        max-width: 200px;
    }

    .topppro-category-item {
        &:nth-child(2n) {
            border-right: none;
        }

        &:nth-child(1n) {
            border-right: 1px solid #ddd;
        }

        &:nth-child(n+2) {
            border-bottom: 1px solid #ddd;
        }
    }

    .topppro-category-image {
        height: 80px;
    }

    .topppro-category-title {
        font-size: 11px;
        padding: 6px;
    }
}


