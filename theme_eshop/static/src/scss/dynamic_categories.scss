/* Dynamic Categories Snippet Styles */

.s_dynamic_categories {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    
    .dynamic-category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 20px;
        max-width: 1000px;
        margin: 0 auto;

        @media (max-width: 768px) {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        @media (max-width: 576px) {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
    }

    /* Category count specific styles */
    &[data-category-count="6"] .dynamic-category-grid {
        max-width: 800px;

        .dynamic-category-item:nth-child(n+7) {
            display: none;
        }
    }

    &[data-category-count="8"] .dynamic-category-grid {
        max-width: 1000px;
    }

    /* Grid layout options */
    &[data-grid-layout="fixed"] .dynamic-category-grid {
        grid-template-columns: repeat(4, 1fr);

        @media (max-width: 768px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 576px) {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    .dynamic-category-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        
        &:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            
            .dynamic-category-image {
                transform: scale(1.1);
            }
            
            .dynamic-category-overlay {
                opacity: 1;
            }
            
            .dynamic-category-title {
                color: #007bff;
            }
        }
    }
    
    .dynamic-category-link {
        display: block;
        text-decoration: none;
        color: inherit;
        
        &:hover {
            text-decoration: none;
            color: inherit;
        }
    }
    
    .dynamic-category-image-wrapper {
        position: relative;
        height: 140px;
        overflow: hidden;
        
        @media (max-width: 576px) {
            height: 120px;
        }
    }
    
    .dynamic-category-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.4s ease;
    }
    
    .dynamic-category-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 123, 255, 0.3));
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
        padding: 10px;
    }
    
    .dynamic-category-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }
    
    .dynamic-category-count {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        i {
            color: #007bff;
        }
    }
    
    .dynamic-category-title {
        padding: 15px;
        text-align: center;
        font-weight: 600;
        font-size: 0.9rem;
        color: #333;
        transition: color 0.3s ease;
        line-height: 1.2;
        
        @media (max-width: 576px) {
            padding: 12px;
            font-size: 0.8rem;
        }
    }
}

/* Enhanced animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.s_dynamic_categories .dynamic-category-item {
    animation: fadeInUp 0.6s ease forwards;
    
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
    &:nth-child(6) { animation-delay: 0.6s; }
    &:nth-child(7) { animation-delay: 0.7s; }
    &:nth-child(8) { animation-delay: 0.8s; }
}

/* Loading state */
.dynamic-category-loading {
    .dynamic-category-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Accessibility improvements */
.s_dynamic_categories {
    .dynamic-category-link:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
        border-radius: 12px;
    }
    
    .dynamic-category-item:focus-within {
        transform: translateY(-4px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .s_dynamic_categories {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        
        .dynamic-category-item {
            background: #3a3a3a;
            color: #fff;
        }
        
        .dynamic-category-title {
            color: #fff;
            
            &:hover {
                color: #74b9ff;
            }
        }
        
        .dynamic-category-count {
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
        }
    }
}

/* Print styles */
@media print {
    .s_dynamic_categories {
        .dynamic-category-overlay {
            display: none;
        }
        
        .dynamic-category-item {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
}
