#!/usr/bin/env python3
"""
Create a test product with price hiding enabled
"""

import subprocess
import time

def create_test_product():
    """Create a test product via Odoo shell"""
    
    python_code = '''
import odoo
from odoo import api, SUPERUSER_ID

try:
    registry = odoo.registry('odoo')
    with registry.cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Create a test product with price hiding
        product = env['product.template'].create({
            'name': 'Test Product - Price Hidden',
            'list_price': 299.99,
            'website_hide_price': True,
            'website_hide_price_message': 'Special pricing available! Contact us for details.',
            'is_published': True,
            'website_published': True,
            'sale_ok': True,
            'type': 'consu',
            'categ_id': env.ref('product.product_category_all').id,
        })
        
        print(f"✅ Created test product: {product.name}")
        print(f"   ID: {product.id}")
        print(f"   Price: ${product.list_price}")
        print(f"   Hide Price: {product.website_hide_price}")
        print(f"   Custom Message: {product.website_hide_price_message}")
        print(f"   Published: {product.website_published}")
        
        # Test the methods
        should_hide = product._should_hide_price()
        message = product._get_hide_price_message()
        print(f"   Should hide price: {should_hide}")
        print(f"   Hide price message: {message}")
        
        # Create another product without price hiding for comparison
        normal_product = env['product.template'].create({
            'name': 'Normal Product - Price Visible',
            'list_price': 199.99,
            'website_hide_price': False,
            'is_published': True,
            'website_published': True,
            'sale_ok': True,
            'type': 'consu',
            'categ_id': env.ref('product.product_category_all').id,
        })
        
        print(f"\\n✅ Created normal product: {normal_product.name}")
        print(f"   ID: {normal_product.id}")
        print(f"   Price: ${normal_product.list_price}")
        print(f"   Hide Price: {normal_product.website_hide_price}")
        
        cr.commit()
        print("\\n🎉 Test products created successfully!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    # Write the script to a temporary file
    with open('/tmp/create_test_product.py', 'w') as f:
        f.write(python_code)
    
    # Copy the script to the container and run it
    subprocess.run("docker cp /tmp/create_test_product.py odoo-odoo-1:/tmp/create_test_product.py", shell=True)
    
    # Run the script
    result = subprocess.run("docker exec odoo-odoo-1 python3 /tmp/create_test_product.py", 
                          shell=True, capture_output=True, text=True, timeout=30)
    
    print("📋 Test Product Creation Results:")
    print("-" * 50)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print("Errors:")
        print(result.stderr)
    
    return result.returncode == 0

def main():
    print("🛍️  CREATING TEST PRODUCTS FOR PRICE HIDING")
    print("=" * 50)
    
    success = create_test_product()
    
    if success:
        print("\n🎯 TESTING INSTRUCTIONS:")
        print("1. Go to http://localhost:8069/shop")
        print("2. Look for 'Test Product - Price Hidden'")
        print("3. You should see the custom message instead of price")
        print("4. Compare with 'Normal Product - Price Visible'")
        print("5. Click on the hidden price product to see detail page")
        print("\n✅ Products are ready for testing!")
    else:
        print("\n❌ Failed to create test products")
        print("Please check the errors above")

if __name__ == "__main__":
    main()
