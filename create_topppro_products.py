#!/usr/bin/env python3
"""
Script to create sample Topp Pro audio equipment products in Odoo
"""

import xmlrpc.client
import base64
import requests

# Odoo connection settings
url = 'http://localhost:8069'
db = 'odoo'
username = 'admin'
password = 'admin'

def connect_odoo():
    """Connect to Odoo and return the models object"""
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    return models, uid

def get_category_id(models, uid, category_name):
    """Get category ID by name"""
    category_ids = models.execute_kw(db, uid, password, 'product.category', 'search', 
                                   [[['name', '=', category_name]]])
    return category_ids[0] if category_ids else None

def create_product(models, uid, name, category_name, price, description, is_published=True):
    """Create a product"""
    category_id = get_category_id(models, uid, category_name)
    
    product_data = {
        'name': name,
        'list_price': price,
        'description_sale': description,
        'is_published': is_published,
        'website_published': is_published,
    }
    
    if category_id:
        product_data['categ_id'] = category_id
    
    product_id = models.execute_kw(db, uid, password, 'product.template', 'create', [product_data])
    print(f"Created product: {name} (ID: {product_id}) in category: {category_name}")
    return product_id

def create_sample_products():
    """Create sample Topp Pro products"""
    models, uid = connect_odoo()
    
    # Sample products based on Topp Pro website
    products = [
        # Mixers
        {
            'name': 'DM48.20 Professional Digital Mixer',
            'category': 'Digital Mixers',
            'price': 2499.00,
            'description': 'Professional Digital Mixer with Optional Dante Digital Card. 48 channels, advanced DSP processing, and professional audio quality.'
        },
        {
            'name': 'MX1204 Compact Mixer',
            'category': 'Compact Mixers',
            'price': 299.00,
            'description': '12-channel compact mixer with built-in effects and USB connectivity. Perfect for small venues and home studios.'
        },
        {
            'name': 'TPM1000 Powered Mixer',
            'category': 'Powered Mixers',
            'price': 899.00,
            'description': '1000W powered mixer with 8 channels, built-in amplifier, and comprehensive EQ controls.'
        },
        
        # Speakers
        {
            'name': 'X15A Active Speaker',
            'category': 'Active Speakers',
            'price': 599.00,
            'description': '15" active speaker with 500W power, professional sound quality, and rugged construction.'
        },
        {
            'name': 'KS12 Passive Speaker',
            'category': 'Passive Speakers',
            'price': 399.00,
            'description': '12" passive speaker with high-quality drivers and professional-grade components.'
        },
        
        # Systems
        {
            'name': 'MegaSys 1 Active System',
            'category': 'Line Array Systems',
            'price': 4999.00,
            'description': 'Active System, DSP Enhanced with 8000W Peak Power. Professional line array system for large venues.'
        },
        {
            'name': 'L-ARRAY Line Array System',
            'category': 'Line Array Systems',
            'price': 3999.00,
            'description': 'Line Array System with 3000W Peak Power. Modular design for versatile sound reinforcement.'
        },
        {
            'name': 'EZ500 Event System',
            'category': 'Event Series',
            'price': 1299.00,
            'description': 'Complete event system with speakers, mixer, and accessories. Perfect for mobile DJs and events.'
        },
        
        # Wireless Systems
        {
            'name': 'TMW-200 Wireless Microphone',
            'category': 'Wireless Microphones',
            'price': 299.00,
            'description': 'Professional wireless microphone system with clear audio transmission and long battery life.'
        },
        {
            'name': 'WTX-100 Wireless Transmitter',
            'category': 'Wireless Transmitters',
            'price': 199.00,
            'description': 'Compact wireless transmitter with reliable signal transmission and easy setup.'
        },
        
        # Power Amplifiers
        {
            'name': 'TPE2000 Professional Amplifier',
            'category': 'Professional Amplifiers',
            'price': 799.00,
            'description': '2000W professional power amplifier with advanced protection circuits and high efficiency.'
        },
        {
            'name': 'HVA500 Installation Amplifier',
            'category': 'Installation Amplifiers',
            'price': 499.00,
            'description': '500W installation amplifier designed for permanent installations with remote control capability.'
        },
        
        # Modules Processors
        {
            'name': 'DANTE-MOD Digital Module',
            'category': 'Digital Processors',
            'price': 599.00,
            'description': 'Dante digital audio module for network audio distribution and processing.'
        },
        {
            'name': 'TAC-EQ Audio Module',
            'category': 'Audio Modules',
            'price': 299.00,
            'description': 'Professional audio equalizer module with precise frequency control.'
        },
        
        # Install
        {
            'name': 'I8 Installation Speaker',
            'category': 'Installation Speakers',
            'price': 199.00,
            'description': '8" installation speaker designed for ceiling or wall mounting in commercial spaces.'
        },
        {
            'name': 'TMA100 Installation Amplifier',
            'category': 'Installation Amplifiers',
            'price': 399.00,
            'description': '100W installation amplifier with multiple input options and zone control.'
        }
    ]
    
    # Create all products
    for product in products:
        create_product(
            models, uid,
            product['name'],
            product['category'],
            product['price'],
            product['description']
        )
    
    print(f"\n✅ Created {len(products)} Topp Pro products successfully!")

if __name__ == '__main__':
    create_sample_products()
