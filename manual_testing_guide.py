#!/usr/bin/env python3
"""
Manual Testing Guide for bi_website_hide_price module
Provides step-by-step instructions for testing all functionality
"""

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"📋 {title}")
    print("="*60)

def print_step(step_num, title, description):
    """Print a formatted step"""
    print(f"\n{step_num}. {title}")
    print("-" * (len(title) + 4))
    print(description)

def main():
    print("🚀 BI WEBSITE HIDE PRICE - MANUAL TESTING GUIDE")
    print("="*60)
    print("This guide will help you test all features of the bi_website_hide_price module")
    print("Make sure your Odoo instance is running at http://localhost:8069")
    
    print_header("PREREQUISITES")
    print("✅ Odoo 18.0 instance running")
    print("✅ Fresh database created")
    print("✅ bi_website_hide_price module available")
    
    print_header("STEP-BY-STEP TESTING PROCESS")
    
    print_step(1, "DATABASE SETUP", 
        """• Open http://localhost:8069 in your browser
• Create a new database (if not already done)
• Choose a database name (e.g., 'test_hide_price')
• Set admin password
• Select your country and language
• Wait for database initialization""")
    
    print_step(2, "INSTALL REQUIRED MODULES",
        """• Go to Apps menu (top navigation)
• Remove 'Apps' filter to see all modules
• Search and install these modules in order:
  1. 'Website' - Core website functionality
  2. 'eCommerce' - Online shop functionality
  3. 'bi_website_hide_price' - Our custom module
• Wait for each installation to complete""")
    
    print_step(3, "CREATE TEST PRODUCTS",
        """• Go to Sales > Products > Products
• Create 2-3 test products with:
  - Name (e.g., 'Test Product 1')
  - Sales Price (e.g., $100.00)
  - Can be Sold: ✓
  - Published on Website: ✓
• Save each product""")
    
    print_step(4, "TEST DEFAULT BEHAVIOR",
        """• Go to Website (top navigation)
• Visit the Shop page
• Verify that:
  ✅ Products are visible
  ✅ Prices are displayed normally
  ✅ 'Add to Cart' buttons are present
• Take note of current behavior""")
    
    print_step(5, "CONFIGURE PRODUCT-LEVEL HIDING",
        """• Go back to Sales > Products > Products
• Edit one of your test products
• In the Sales tab, find 'Website Price Visibility' section
• Check the 'Website Hide Price' checkbox
• Enter a custom message (e.g., 'Contact us for pricing')
• Save the product""")
    
    print_step(6, "TEST PRODUCT-LEVEL HIDING",
        """• Go back to Website > Shop
• Find the product you just configured
• Verify that:
  ✅ Price is hidden for that specific product
  ✅ Custom message is displayed instead
  ✅ 'Add to Cart' is replaced with 'Contact Us'
  ✅ Other products still show prices normally""")
    
    print_step(7, "TEST WEBSITE-LEVEL SETTINGS",
        """• Go to Website > Configuration > Settings
• Look for 'Website Price Visibility' section
• Try different options:
  - 'Hide Price for All Users'
  - 'Hide Price Only for Guest Users'
• Save settings after each change""")
    
    print_step(8, "TEST GLOBAL HIDING",
        """• After setting 'Hide Price for All Users':
• Visit Website > Shop
• Verify that:
  ✅ ALL product prices are hidden
  ✅ Custom messages appear
  ✅ All 'Add to Cart' buttons become 'Contact Us'""")
    
    print_step(9, "TEST GUEST VS LOGGED-IN USERS",
        """• Set option to 'Hide Price Only for Guest Users'
• Test as guest user (not logged in):
  ✅ Prices should be hidden
• Log in as admin user
• Test as logged-in user:
  ✅ Prices should be visible""")
    
    print_step(10, "TEST CONTACT US FUNCTIONALITY",
        """• When prices are hidden, click 'Contact Us' buttons
• Verify that:
  ✅ Contact form opens or redirects properly
  ✅ Product information is included in contact context""")
    
    print_header("EXPECTED RESULTS SUMMARY")
    print("""
✅ PRODUCT-LEVEL CONTROL:
   • Individual products can have prices hidden
   • Custom messages display instead of prices
   • 'Add to Cart' becomes 'Contact Us' for hidden products

✅ WEBSITE-LEVEL CONTROL:
   • Global setting to hide all prices
   • Option to hide prices only for guest users
   • Logged-in users can see prices when configured

✅ USER EXPERIENCE:
   • Clean interface without prices when hidden
   • Clear call-to-action with 'Contact Us' buttons
   • Custom messages provide context to users

✅ FUNCTIONALITY:
   • Works on product listing pages
   • Works on product detail pages
   • Maintains website performance
   • Compatible with existing themes
    """)
    
    print_header("TROUBLESHOOTING")
    print("""
❌ If module doesn't appear in Apps:
   • Check docker-compose.yml has the module mounted
   • Restart Odoo: docker-compose restart
   • Update Apps list: Apps > Update Apps List

❌ If prices still show after hiding:
   • Clear browser cache
   • Check product-level settings
   • Verify website-level configuration
   • Check user login status

❌ If 'Contact Us' doesn't work:
   • Ensure mail module is installed
   • Check contact form configuration
   • Verify email settings in Odoo
    """)
    
    print_header("SUCCESS CRITERIA")
    print("""
🎯 The module is working correctly if:
   ✅ Prices can be hidden at product level
   ✅ Prices can be hidden at website level
   ✅ Custom messages display properly
   ✅ 'Contact Us' buttons replace 'Add to Cart'
   ✅ Different behavior for guest vs logged-in users
   ✅ No errors in browser console or Odoo logs
    """)
    
    print("\n" + "="*60)
    print("🎉 TESTING COMPLETE!")
    print("If all tests pass, your bi_website_hide_price module is working perfectly!")
    print("="*60)

if __name__ == "__main__":
    main()
