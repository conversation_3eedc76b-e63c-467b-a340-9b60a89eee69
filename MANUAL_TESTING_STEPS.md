# 🧪 Manual Testing Guide - bi_website_hide_price Module

## 🎯 Current Status
✅ Module installed successfully  
✅ Web interface accessible at http://localhost:8069  
✅ Ready for manual testing  

## 📋 Step-by-Step Testing Instructions

### STEP 1: Login to Odoo
1. **Open browser** (already opened): http://localhost:8069
2. **Login credentials**:
   - Email: `admin`
   - Password: `admin`
3. **Click "Log in"**

### STEP 2: Access Apps Menu (Verify Module Installation)
1. Click on **"Apps"** in the main menu
2. **Remove the "Apps" filter** (click the X next to "Apps" in the search bar)
3. **Search for**: `bi_website_hide_price`
4. **Expected result**: You should see the module listed as "Installed"
5. **Alternative search**: Search for `vear` (the author name)

### STEP 3: Test Product Configuration
1. Go to **Sales** → **Products** → **Products**
2. **Open any existing product** OR **create a new product**:
   - Click "Create" if making new product
   - Name: "Test Hide Price Product"
   - Sale Price: 100.00
3. **Go to the "Sales" tab**
4. **Look for "Website Price Visibility" section**
5. **Expected fields**:
   - ✅ **"Website Hide Price"** checkbox
   - ✅ **"Custom Message"** text field (should appear when checkbox is checked)

### STEP 4: Test Field Behavior
1. **Check the "Website Hide Price" checkbox**
2. **Verify**: "Custom Message" field should appear
3. **Enter custom message**: "Contact us for special pricing!"
4. **Save the product**
5. **Uncheck the checkbox**
6. **Verify**: "Custom Message" field should disappear
7. **Check the checkbox again**
8. **Verify**: "Custom Message" field reappears with saved text

### STEP 5: Test Website Integration (Limited)
1. **Click "Go to Website"** button (top right)
2. **Navigate to Shop** (if available)
3. **Find your test product**
4. **Current limitation**: Frontend price hiding is not yet active (templates disabled)
5. **Expected**: You should see normal product display for now

### STEP 6: Verify Module Data
1. **Go back to backend** (click Odoo logo)
2. **Check Settings** → **Technical** → **Database Structure** → **Models**
3. **Search for**: `product.template`
4. **Click on it** → **Fields**
5. **Search for**: `website_hide_price`
6. **Expected**: You should see the new fields listed

## ✅ Success Criteria

### Backend Testing (Should Work):
- [ ] Module appears as "Installed" in Apps
- [ ] Product form shows "Website Price Visibility" section
- [ ] "Website Hide Price" checkbox works
- [ ] "Custom Message" field appears/disappears correctly
- [ ] Product saves with hide price settings
- [ ] Fields are properly integrated in product form

### Frontend Testing (Currently Limited):
- [ ] Website is accessible
- [ ] Products are visible on website
- [ ] Price hiding not yet active (expected limitation)

## 🐛 Troubleshooting

### If Module Not Found in Apps:
1. Enable **Developer Mode**: Settings → Activate Developer Mode
2. Go to **Apps** → **Update Apps List**
3. Search again for `bi_website_hide_price`

### If Fields Not Visible in Product:
1. Make sure you're in the **"Sales" tab** of the product form
2. Scroll down to find **"Website Price Visibility"** section
3. Try refreshing the page (F5)

### If Website Not Working:
1. Check if Odoo is running: `docker-compose ps`
2. Restart if needed: `docker-compose restart odoo`
3. Wait 30 seconds and try again

## 📊 Expected Test Results

### ✅ What Should Work Now:
- Module installation and visibility
- Product field configuration
- Backend form functionality
- Field visibility logic

### ⏳ What's Coming Next:
- Frontend price hiding templates
- Website configuration settings
- Complete price hiding functionality
- "Contact Us" button integration

## 🎯 Testing Completion

Once you've completed these steps, you'll have verified that:
1. The module is properly installed
2. Backend functionality is working
3. Product configuration is available
4. The foundation is ready for frontend development

**Ready to test? Start with Step 1! 🚀**
