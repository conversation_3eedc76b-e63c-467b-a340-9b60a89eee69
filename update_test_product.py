#!/usr/bin/env python3
"""
Update test product with catalog-style message
"""

import subprocess

def update_test_product():
    """Update the test product message"""
    
    python_code = '''
import odoo
from odoo import api, SUPERUSER_ID

try:
    registry = odoo.registry('odoo')
    with registry.cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Find and update the test product
        product = env['product.template'].search([('name', '=', 'Test Product - Price Hidden')])
        if product:
            product.write({
                'website_hide_price_message': 'Available in store'
            })
            print(f"✅ Updated product: {product.name}")
            print(f"   New message: {product.website_hide_price_message}")
        else:
            print("❌ Test product not found")
        
        cr.commit()
        print("\\n🎉 Product updated successfully!")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    # Write the script to a temporary file
    with open('/tmp/update_test_product.py', 'w') as f:
        f.write(python_code)
    
    # Copy the script to the container and run it
    subprocess.run("docker cp /tmp/update_test_product.py odoo-odoo-1:/tmp/update_test_product.py", shell=True)
    
    # Run the script
    result = subprocess.run("docker exec odoo-odoo-1 python3 /tmp/update_test_product.py", 
                          shell=True, capture_output=True, text=True, timeout=30)
    
    print("📋 Product Update Results:")
    print("-" * 50)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print("Errors:")
        print(result.stderr)
    
    return result.returncode == 0

def main():
    print("📝 UPDATING TEST PRODUCT FOR CATALOG STYLE")
    print("=" * 50)
    
    success = update_test_product()
    
    if success:
        print("\n🎯 CATALOG STYLE TESTING:")
        print("1. Go to http://localhost:8069/shop")
        print("2. Look for 'Test Product - Price Hidden'")
        print("3. Should now show: 'Available in store'")
        print("4. No contact buttons - pure catalog style")
        print("\n✅ Ready for catalog testing!")
    else:
        print("\n❌ Failed to update test product")

if __name__ == "__main__":
    main()
