#!/usr/bin/env python3
"""
Script to set up Topp Pro audio equipment categories in Odoo
"""

import xmlrpc.client

# Odoo connection settings
url = 'http://localhost:8069'
db = 'odoo'
username = 'admin'
password = 'admin'

def connect_odoo():
    """Connect to Odoo and return the models object"""
    common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
    uid = common.authenticate(db, username, password, {})
    models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
    return models, uid

def create_category(models, uid, name, parent_id=None):
    """Create a product category"""
    category_data = {
        'name': name,
    }
    if parent_id:
        category_data['parent_id'] = parent_id

    category_id = models.execute_kw(db, uid, password, 'product.category', 'create', [category_data])
    print(f"Created category: {name} (ID: {category_id})")
    return category_id

def setup_topppro_categories():
    """Set up all Topp Pro categories"""
    models, uid = connect_odoo()
    
    # Main categories with their subcategories
    categories_structure = {
        'Mixers': [
            'Digital Mixers',
            'Compact Mixers',
            'Powered Mixers'
        ],
        'Speakers': [
            'Active Speakers',
            'Passive Speakers'
        ],
        'Systems': [
            'Line Array Systems',
            'Event Series',
            'Smart Array Systems'
        ],
        'Wireless Systems': [
            'Wireless Microphones',
            'Wireless Transmitters'
        ],
        'Power Amplifiers': [
            'Professional Amplifiers',
            'Installation Amplifiers'
        ],
        'Modules Processors': [
            'Digital Processors',
            'Audio Modules',
            'Control Modules'
        ],
        'Install': [
            'Installation Speakers',
            'Installation Amplifiers',
            'Installation Accessories'
        ],
        'Legacy Models': []
    }
    
    # Create main categories and subcategories
    for main_cat_name, subcategories in categories_structure.items():
        # Create main category
        main_cat_id = create_category(models, uid, main_cat_name)

        # Create subcategories
        for subcat_name in subcategories:
            create_category(models, uid, subcat_name, parent_id=main_cat_id)
    
    print("\n✅ All Topp Pro categories created successfully!")

if __name__ == '__main__':
    setup_topppro_categories()
