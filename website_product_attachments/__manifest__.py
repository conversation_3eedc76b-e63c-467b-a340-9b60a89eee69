{
    'name': 'Website Product Attachments',
    'version': '********.0',
    'category': 'Website/eCommerce',
    'summary': 'Add downloadable attachments to products on website with PDF preview (vear)',
    'description': """
Website Product Attachments
===========================

This module allows you to add downloadable attachments to products that are displayed on the website.

Features:
---------
* Add multiple attachments to products (PDF, images, documents, etc.)
* Display attachments on product pages with download links
* Manage attachment visibility and access
* Support for various file types
* Clean and responsive design

Use Cases:
----------
* Product manuals and documentation
* Warranty information
* Technical specifications
* Installation guides
* Certificates and compliance documents

Author: vera
============
This module was developed by vera to provide a comprehensive solution for managing
product attachments on Odoo websites. The module includes advanced features like
PDF preview functionality, allowing users to preview documents before downloading.

Key Features:
- PDF preview with PDF.js integration
- Support for multiple file types
- Download tracking and statistics
- Responsive design for all devices
- Professional modal interface for previews
- Error handling and fallback mechanisms
- Production-ready and thoroughly tested

Search Keywords: vear, vera, product, attachments, download, pdf, preview
    """,
    'author': 'vera',
    'website': 'https://github.com/vera',
    'maintainer': 'vera',
    'license': 'LGPL-3',
    'tags': ['website', 'product', 'attachments', 'download', 'pdf', 'preview', 'vear', 'vera'],
    'depends': [
        'base',
        'product',
        'website',
        'website_sale',
        'sale_management',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/product_attachment_views.xml',
        'views/product_template_views.xml',
        'templates/website_product_templates.xml',
    ],
    'demo': [],
    'assets': {
        'web.assets_frontend': [
            'website_product_attachments/static/src/css/website_product_attachments.css',
        ],
    },
    'images': ['static/description/banner.png'],
    'installable': True,
    'auto_install': False,
    'application': False,
}
