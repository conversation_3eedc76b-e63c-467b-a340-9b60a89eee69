<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Product Template Form View -->
    <record id="view_product_template_form_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit.attachments</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <!-- Add button in button box -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <button class="oe_stat_button" type="object" name="action_view_attachments" icon="fa-paperclip">
                    <field string="Attachments" name="attachment_count" widget="statinfo"/>
                </button>
            </xpath>
            
            <!-- Add attachments page -->
            <xpath expr="//notebook" position="inside">
                <page string="Attachments" name="attachments">
                    <field name="attachment_ids" context="{'default_product_tmpl_id': id}">
                        <list editable="bottom" default_order="sequence,name">
                            <field name="sequence" widget="handle"/>
                            <field name="name" required="1"/>
                            <field name="description"/>
                            <field name="attachment_file" filename="filename" required="1"/>
                            <field name="filename" required="1"/>
                            <field name="file_size" readonly="1" widget="integer"/>
                            <field name="website_published" widget="boolean_toggle"/>
                            <field name="active" widget="boolean_toggle"/>
                        </list>
                        <form>
                            <group>
                                <group>
                                    <field name="name" required="1"/>
                                    <field name="sequence"/>
                                    <field name="website_published"/>
                                </group>
                                <group>
                                    <field name="attachment_file" filename="filename" required="1"/>
                                    <field name="filename" required="1"/>
                                    <field name="file_size" readonly="1" widget="integer"/>
                                    <field name="file_type" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <field name="description" placeholder="Description of the attachment..."/>
                            </group>
                        </form>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

    <!-- Inherit Product Template Tree View -->
    <record id="view_product_template_tree_inherit" model="ir.ui.view">
        <field name="name">product.template.tree.inherit.attachments</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='list_price']" position="after">
                <field name="attachment_count" string="Attachments" optional="hide"/>
            </xpath>
        </field>
    </record>
</odoo>
