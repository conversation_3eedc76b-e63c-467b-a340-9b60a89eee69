<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Attachment Tree View -->
    <record id="view_product_attachment_tree" model="ir.ui.view">
        <field name="name">product.attachment.tree</field>
        <field name="model">product.attachment</field>
        <field name="arch" type="xml">
            <list string="Product Attachments" default_order="sequence,name">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="filename"/>
                <field name="file_size" widget="integer"/>
                <field name="file_type"/>
                <field name="product_tmpl_id"/>
                <field name="website_published" widget="boolean_toggle"/>
                <field name="download_count"/>
                <field name="active" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Product Attachment Form View -->
    <record id="view_product_attachment_form" model="ir.ui.view">
        <field name="name">product.attachment.form</field>
        <field name="model">product.attachment</field>
        <field name="arch" type="xml">
            <form string="Product Attachment">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" required="1"/>
                            <field name="product_tmpl_id" required="1"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="website_published"/>
                            <field name="download_count" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Description of the attachment..."/>
                    </group>
                    <group string="File Information">
                        <group>
                            <field name="attachment_file" filename="filename" required="1"/>
                            <field name="filename" required="1"/>
                        </group>
                        <group>
                            <field name="file_size" readonly="1" widget="integer"/>
                            <field name="file_type" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Attachment Search View -->
    <record id="view_product_attachment_search" model="ir.ui.view">
        <field name="name">product.attachment.search</field>
        <field name="model">product.attachment</field>
        <field name="arch" type="xml">
            <search string="Product Attachments">
                <field name="name" string="Name"/>
                <field name="filename" string="Filename"/>
                <field name="product_tmpl_id" string="Product"/>
                <field name="file_type" string="File Type"/>
                <separator/>
                <filter name="published" string="Published" domain="[('website_published', '=', True)]"/>
                <filter name="unpublished" string="Unpublished" domain="[('website_published', '=', False)]"/>
                <separator/>
                <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter name="group_product" string="Product" context="{'group_by': 'product_tmpl_id'}"/>
                    <filter name="group_file_type" string="File Type" context="{'group_by': 'file_type'}"/>
                    <filter name="group_published" string="Published Status" context="{'group_by': 'website_published'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Product Attachment Action -->
    <record id="action_product_attachment" model="ir.actions.act_window">
        <field name="name">Product Attachments</field>
        <field name="res_model">product.attachment</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_product_attachment_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first product attachment!
            </p>
            <p>
                Product attachments allow you to provide downloadable files for your products
                such as user manuals, warranties, technical specifications, and more.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_product_attachment"
              name="Product Attachments"
              parent="sale.prod_config_main"
              action="action_product_attachment"
              sequence="50"/>
</odoo>
