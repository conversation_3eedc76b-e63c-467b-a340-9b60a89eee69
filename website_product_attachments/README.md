# Website Product Attachments

A custom Odoo 18 Community Edition module that allows adding downloadable attachments to products displayed on the website.

## Features

### Core Functionality
- **Multiple File Attachments**: Add unlimited files to any product (PDF, images, documents, etc.)
- **Website Display**: Attachments are automatically displayed on product pages
- **Download Tracking**: Track how many times each attachment has been downloaded
- **File Management**: Easy upload, organize, and manage attachments from the product form
- **Responsive Design**: Mobile-friendly attachment display

### File Support
- **All File Types**: Support for PDF, Word, Excel, PowerPoint, images, videos, audio, archives, and more
- **File Size Validation**: Maximum 50MB per file with clear error messages
- **MIME Type Detection**: Automatic file type detection and appropriate icons
- **Human-Readable Sizes**: Display file sizes in B, KB, MB, GB format

### Website Features
- **Multiple Layout Options**: 
  - Card layout (default)
  - Compact list layout
  - Tab layout (alternative)
- **Smart Icons**: Different icons for different file types (PDF, Word, Excel, etc.)
- **Download Analytics**: Track downloads with Google Analytics and Facebook Pixel integration
- **SEO Friendly**: Proper file naming and download URLs

### Security & Access Control
- **Role-Based Access**: Different permissions for public, portal users, sales users, and managers
- **Publication Control**: Choose which attachments are visible on the website
- **Product Visibility**: Only show attachments for published products
- **Secure Downloads**: Protected download URLs with proper access control

## Installation

1. Copy the `website_product_attachments` folder to your Odoo addons directory
2. Update the apps list: Go to Apps > Update Apps List
3. Search for "Website Product Attachments" and install the module

## Dependencies

This module depends on the following Odoo modules:
- `base` - Base Odoo functionality
- `product` - Product management
- `website` - Website functionality
- `website_sale` - eCommerce functionality
- `sale_management` - Sales management

## Usage

### Adding Attachments to Products

1. **Via Product Form**:
   - Go to Sales > Products > Products
   - Open any product
   - Click on the "Attachments" tab
   - Add files using the attachment list

2. **Via Attachments Button**:
   - In the product form, click the "Attachments" smart button
   - Create new attachments or manage existing ones

### Managing Attachments

1. **Global Management**:
   - Go to Sales > Configuration > Product Attachments
   - View, edit, or delete all product attachments

2. **Attachment Fields**:
   - **Name**: Display name for the attachment
   - **Description**: Optional description shown on website
   - **File**: The actual file to upload
   - **Filename**: Original filename (auto-filled)
   - **Sequence**: Order of display on website
   - **Published on Website**: Control visibility on website
   - **Active**: Archive/unarchive attachments

### Website Display

Attachments automatically appear on product pages when:
- The attachment is marked as "Published on Website"
- The attachment is "Active"
- The product is published on the website

### Layout Options

The module provides three layout options for displaying attachments:

1. **Card Layout** (Default): Attractive card-based display
2. **Compact Layout**: Simple list format
3. **Tab Layout**: Attachments in a separate tab

To switch layouts, activate/deactivate the corresponding template views in the website editor.

## Configuration

### File Size Limits
- Default maximum file size: 50MB
- Can be modified in the model validation method

### Security Groups
- **Product Attachment Manager**: Full access to all attachments
- **Sales Manager**: Full access to attachments
- **Sales User**: Can create and edit attachments
- **Internal User**: Read-only access
- **Portal User**: Can view published attachments
- **Public User**: Can view and download published attachments

### Customization

#### Custom File Icons
Add custom icons by modifying the `get_file_icon()` method in `models/product_attachment.py`.

#### Custom Layouts
Create custom templates by inheriting from the existing website templates.

#### Custom Styling
Modify `static/src/css/website_product_attachments.css` for custom styling.

## Technical Details

### Models
- **product.attachment**: Main model for storing attachments
- **product.template**: Extended with attachment relationship

### Controllers
- **ProductAttachmentController**: Handles file downloads and previews
- **ProductAttachmentPortal**: Portal integration for customer access

### Views
- Backend forms and lists for attachment management
- Website templates for displaying attachments
- Smart buttons and statistics

### Security
- Access control lists (ACL) for different user groups
- Record rules for data access control
- Secure file download handling

## Troubleshooting

### Common Issues

1. **Files not appearing on website**:
   - Check if attachment is marked as "Published on Website"
   - Verify the product is published
   - Ensure attachment is "Active"

2. **Download not working**:
   - Check file permissions
   - Verify the file was uploaded correctly
   - Check browser console for errors

3. **File size errors**:
   - Ensure files are under 50MB
   - Check server upload limits

### Support

For technical support or customization requests, please contact your Odoo developer or system administrator.

## License

This module is licensed under LGPL-3.

## Version History

- **1.0.0**: Initial release for Odoo 18 Community Edition
  - Basic attachment functionality
  - Website display
  - Download tracking
  - Multiple layout options
  - Security and access control
