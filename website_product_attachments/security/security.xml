<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups -->
    <record id="group_product_attachment_manager" model="res.groups">
        <field name="name">Product Attachment Manager</field>
        <field name="category_id" ref="base.module_category_sales_sales"/>
    </record>

    <!-- Record Rules -->
    <record id="product_attachment_rule_public" model="ir.rule">
        <field name="name">Product Attachment: Public Access</field>
        <field name="model_id" ref="model_product_attachment"/>
        <field name="domain_force">[
            ('active', '=', True),
            ('website_published', '=', True),
            ('product_tmpl_id.is_published', '=', True)
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_public'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="product_attachment_rule_portal" model="ir.rule">
        <field name="name">Product Attachment: Portal Access</field>
        <field name="model_id" ref="model_product_attachment"/>
        <field name="domain_force">[
            ('active', '=', True),
            ('website_published', '=', True),
            ('product_tmpl_id.is_published', '=', True)
        ]</field>
        <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="product_attachment_rule_user" model="ir.rule">
        <field name="name">Product Attachment: Internal User Access</field>
        <field name="model_id" ref="model_product_attachment"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="False"/>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="product_attachment_rule_sales_user" model="ir.rule">
        <field name="name">Product Attachment: Sales User Access</field>
        <field name="model_id" ref="model_product_attachment"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>
</odoo>
