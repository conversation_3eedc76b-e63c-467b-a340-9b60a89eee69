<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Include PDF Preview Assets -->
    <template id="pdf_preview_assets" name="PDF Preview Assets" inherit_id="website.layout">
        <xpath expr="//head" position="inside">
            <link rel="stylesheet" type="text/css" href="/website_product_attachments/static/src/css/pdf_preview.css"/>
            <script type="text/javascript" src="/website_product_attachments/static/src/js/pdf_preview.js"></script>
        </xpath>
    </template>

    <!-- Inherit Website Product Page -->
    <template id="product_attachments_section" inherit_id="website_sale.product" name="Product Attachments Section" priority="10">
        <xpath expr="//div[@id='product_details']//form[@action='/shop/cart/update']" position="after">
            <t t-set="attachments" t-value="product.get_website_attachments()"/>
            <t t-if="attachments">
                <div class="mt-4 mb-3">
                    <h4 class="mb-3">
                        <i class="fa fa-paperclip me-2"/>
                        Downloads &amp; Documentation
                    </h4>
                    <div class="row">
                        <t t-foreach="attachments" t-as="attachment">
                            <div class="col-lg-6 col-md-12 mb-3">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body d-flex align-items-center">
                                        <div class="me-3">
                                            <i t-attf-class="fa #{attachment.get_file_icon()} fa-2x text-primary"/>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="card-title mb-1" t-field="attachment.name"/>
                                            <p class="card-text text-muted small mb-1" t-if="attachment.description" t-field="attachment.description"/>
                                            <small class="text-muted">
                                                <span t-esc="attachment.get_human_readable_size()"/>
                                                <t t-if="attachment.file_type">
                                                    • <span t-esc="attachment.file_type.split('/')[-1].upper()"/>
                                                </t>
                                            </small>
                                        </div>
                                        <div class="ms-3">
                                            <div class="btn-group" role="group">
                                                <t t-if="attachment.is_pdf()">
                                                    <button type="button"
                                                            class="btn btn-outline-primary btn-sm"
                                                            t-attf-onclick="if(typeof openPDFPreview === 'function') { openPDFPreview('/web/content/product.attachment/#{attachment.id}/attachment_file/#{attachment.filename}', '#{attachment.filename}', '/web/content/product.attachment/#{attachment.id}/attachment_file/#{attachment.filename}?download=true'); } else { console.error('PDF preview not available'); }"
                                                            title="Preview PDF file">
                                                        <i class="fa fa-eye me-1"/>
                                                        Preview
                                                    </button>
                                                </t>
                                                <a t-attf-href="/web/content/product.attachment/#{attachment.id}/attachment_file/#{attachment.filename}?download=true"
                                                   class="btn btn-primary btn-sm"
                                                   t-attf-onclick="this.href += '&amp;token=' + Date.now(); return true;">
                                                    <i class="fa fa-download me-1"/>
                                                    Download
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <!-- Alternative compact layout for attachments -->
    <template id="product_attachments_compact" inherit_id="website_sale.product" name="Product Attachments Compact" active="False">
        <xpath expr="//div[@id='product_details']//hr[last()]" position="after">
            <t t-set="attachments" t-value="product.get_website_attachments()"/>
            <t t-if="attachments">
                <div class="mt-4 mb-3">
                    <h5 class="mb-3">
                        <i class="fa fa-paperclip me-2"/>
                        Downloads
                    </h5>
                    <div class="list-group list-group-flush">
                        <t t-foreach="attachments" t-as="attachment">
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div class="d-flex align-items-center">
                                    <i t-attf-class="fa #{attachment.get_file_icon()} me-3 text-primary"/>
                                    <div>
                                        <div class="fw-medium" t-field="attachment.name"/>
                                        <small class="text-muted" t-if="attachment.description" t-field="attachment.description"/>
                                        <br t-if="attachment.description"/>
                                        <small class="text-muted">
                                            <span t-esc="attachment.get_human_readable_size()"/>
                                        </small>
                                    </div>
                                </div>
                                <a t-attf-href="/web/content/product.attachment/#{attachment.id}/attachment_file/#{attachment.filename}?download=true"
                                   class="btn btn-outline-primary btn-sm"
                                   t-attf-onclick="this.href += '&amp;token=' + Date.now(); return true;">
                                    <i class="fa fa-download"/>
                                </a>
                            </div>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <!-- Product Attachments in Product Tabs (Alternative Layout) -->
    <template id="product_attachments_tab" inherit_id="website_sale.product" name="Product Attachments Tab" active="False">
        <xpath expr="//ul[@role='tablist']" position="inside">
            <t t-set="attachments" t-value="product.get_website_attachments()"/>
            <t t-if="attachments">
                <li class="nav-item">
                    <a class="nav-link" href="#attachments_tab" role="tab" data-bs-toggle="tab">
                        <i class="fa fa-paperclip me-1"/>
                        Downloads
                        <span class="badge bg-primary ms-1" t-esc="len(attachments)"/>
                    </a>
                </li>
            </t>
        </xpath>
        <xpath expr="//div[@class='tab-content']" position="inside">
            <t t-set="attachments" t-value="product.get_website_attachments()"/>
            <t t-if="attachments">
                <div class="tab-pane fade" id="attachments_tab" role="tabpanel">
                    <div class="row mt-3">
                        <t t-foreach="attachments" t-as="attachment">
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start">
                                            <i t-attf-class="fa #{attachment.get_file_icon()} fa-2x text-primary me-3 mt-1"/>
                                            <div class="flex-grow-1">
                                                <h6 class="card-title" t-field="attachment.name"/>
                                                <p class="card-text text-muted" t-if="attachment.description" t-field="attachment.description"/>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <span t-esc="attachment.get_human_readable_size()"/>
                                                    </small>
                                                    <a t-attf-href="/web/content/product.attachment/#{attachment.id}/attachment_file/#{attachment.filename}?download=true"
                                                       class="btn btn-primary btn-sm"
                                                       t-attf-onclick="this.href += '&amp;token=' + Date.now(); return true;">
                                                        <i class="fa fa-download me-1"/>
                                                        Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </t>
        </xpath>
    </template>
</odoo>
