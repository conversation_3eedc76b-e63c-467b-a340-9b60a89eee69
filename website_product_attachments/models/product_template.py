# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    attachment_ids = fields.One2many(
        'product.attachment',
        'product_tmpl_id',
        string='Attachments',
        help="Files attached to this product"
    )
    
    attachment_count = fields.Integer(
        string='Attachment Count',
        compute='_compute_attachment_count',
        help="Number of attachments for this product"
    )
    
    website_attachment_count = fields.Integer(
        string='Website Attachment Count',
        compute='_compute_website_attachment_count',
        help="Number of published attachments for this product"
    )
    
    @api.depends('attachment_ids')
    def _compute_attachment_count(self):
        """Compute total number of attachments"""
        for product in self:
            product.attachment_count = len(product.attachment_ids)
    
    @api.depends('attachment_ids.website_published', 'attachment_ids.active')
    def _compute_website_attachment_count(self):
        """Compute number of published attachments"""
        for product in self:
            published_attachments = product.attachment_ids.filtered(
                lambda a: a.active and a.website_published
            )
            product.website_attachment_count = len(published_attachments)
    
    def get_website_attachments(self):
        """Get attachments that should be displayed on website"""
        self.ensure_one()
        return self.attachment_ids.filtered(
            lambda a: a.active and a.website_published
        ).sorted('sequence')
    
    def action_view_attachments(self):
        """Action to view product attachments"""
        self.ensure_one()
        return {
            'name': 'Product Attachments',
            'type': 'ir.actions.act_window',
            'res_model': 'product.attachment',
            'view_mode': 'list,form',
            'domain': [('product_tmpl_id', '=', self.id)],
            'context': {
                'default_product_tmpl_id': self.id,
                'search_default_product_tmpl_id': self.id,
            },
        }
