# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import base64
import mimetypes


class ProductAttachment(models.Model):
    _name = 'product.attachment'
    _description = 'Product Attachment'
    _order = 'sequence, name'

    name = fields.Char(
        string='Name',
        required=True,
        help="Display name for the attachment"
    )
    
    description = fields.Text(
        string='Description',
        help="Description of the attachment"
    )
    
    attachment_file = fields.Binary(
        string='File',
        required=True,
        help="The file to be attached to the product"
    )
    
    filename = fields.Char(
        string='Filename',
        required=True,
        help="Original filename of the attachment"
    )
    
    file_size = fields.Integer(
        string='File Size',
        compute='_compute_file_size',
        store=True,
        help="Size of the file in bytes"
    )
    
    file_type = fields.Char(
        string='File Type',
        compute='_compute_file_type',
        store=True,
        help="MIME type of the file"
    )
    
    product_tmpl_id = fields.Many2one(
        'product.template',
        string='Product Template',
        required=True,
        ondelete='cascade',
        help="Product this attachment belongs to"
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Order of display on the website"
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help="If unchecked, the attachment will not be visible on the website"
    )
    
    website_published = fields.Boolean(
        string='Published on Website',
        default=True,
        help="If checked, the attachment will be visible on the website"
    )
    
    download_count = fields.Integer(
        string='Download Count',
        default=0,
        help="Number of times this attachment has been downloaded"
    )
    
    @api.depends('attachment_file')
    def _compute_file_size(self):
        """Compute the file size from the binary data"""
        for record in self:
            if record.attachment_file:
                # Decode base64 to get actual file size
                try:
                    file_data = base64.b64decode(record.attachment_file)
                    record.file_size = len(file_data)
                except Exception:
                    record.file_size = 0
            else:
                record.file_size = 0
    
    @api.depends('filename')
    def _compute_file_type(self):
        """Compute the MIME type from the filename"""
        for record in self:
            if record.filename:
                mime_type, _ = mimetypes.guess_type(record.filename)
                record.file_type = mime_type or 'application/octet-stream'
            else:
                record.file_type = False
    
    @api.constrains('attachment_file', 'filename')
    def _check_file_size(self):
        """Validate file size (max 50MB)"""
        max_size = 50 * 1024 * 1024  # 50MB in bytes
        for record in self:
            if record.file_size > max_size:
                raise ValidationError(
                    _("File size cannot exceed 50MB. Current size: %.2f MB") 
                    % (record.file_size / (1024 * 1024))
                )
    
    def get_file_icon(self):
        """Return appropriate icon class based on file type"""
        self.ensure_one()
        if not self.file_type:
            return 'fa-file-o'
        
        icon_mapping = {
            'application/pdf': 'fa-file-pdf-o',
            'application/msword': 'fa-file-word-o',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word-o',
            'application/vnd.ms-excel': 'fa-file-excel-o',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel-o',
            'application/vnd.ms-powerpoint': 'fa-file-powerpoint-o',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'fa-file-powerpoint-o',
            'text/plain': 'fa-file-text-o',
            'application/zip': 'fa-file-archive-o',
            'application/x-rar-compressed': 'fa-file-archive-o',
        }
        
        # Check for image types
        if self.file_type.startswith('image/'):
            return 'fa-file-image-o'
        
        # Check for video types
        if self.file_type.startswith('video/'):
            return 'fa-file-video-o'
        
        # Check for audio types
        if self.file_type.startswith('audio/'):
            return 'fa-file-audio-o'
        
        return icon_mapping.get(self.file_type, 'fa-file-o')
    
    def get_human_readable_size(self):
        """Return file size in human readable format"""
        self.ensure_one()
        size = self.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"

    def is_pdf(self):
        """Check if the attachment is a PDF file"""
        self.ensure_one()
        return self.file_type == 'application/pdf'

    def get_preview_url(self):
        """Get URL for PDF preview"""
        self.ensure_one()
        if self.is_pdf():
            return f"/web/content/product.attachment/{self.id}/attachment_file/{self.filename}"
        return False
    
    def increment_download_count(self):
        """Increment the download counter"""
        self.ensure_one()
        self.sudo().write({'download_count': self.download_count + 1})
    
    @api.model
    def create(self, vals):
        """Override create to ensure proper filename handling"""
        if 'filename' not in vals and 'name' in vals:
            vals['filename'] = vals['name']
        return super().create(vals)
