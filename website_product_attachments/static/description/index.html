<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Website Product Attachments</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 40px; }
        .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; background: #f8f9fa; }
        .feature h3 { margin-top: 0; color: #2c3e50; }
        .screenshot { text-align: center; margin: 30px 0; }
        .screenshot img { max-width: 100%; border: 1px solid #ddd; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Website Product Attachments</h1>
        <p>Add downloadable files to your products on the website</p>
    </div>

    <div class="feature">
        <h3>📎 Multiple File Support</h3>
        <p>Upload any type of file: PDFs, documents, images, videos, and more. Each product can have unlimited attachments.</p>
    </div>

    <div class="feature">
        <h3>🌐 Website Integration</h3>
        <p>Attachments automatically appear on product pages with beautiful, responsive design and smart file type icons.</p>
    </div>

    <div class="feature">
        <h3>📊 Download Tracking</h3>
        <p>Track how many times each attachment has been downloaded with built-in analytics support.</p>
    </div>

    <div class="feature">
        <h3>🔒 Security & Access Control</h3>
        <p>Control who can see and download attachments with role-based permissions and publication settings.</p>
    </div>

    <div class="feature">
        <h3>📱 Mobile Friendly</h3>
        <p>Responsive design ensures attachments look great on all devices - desktop, tablet, and mobile.</p>
    </div>

    <div class="feature">
        <h3>⚙️ Easy Management</h3>
        <p>Simple interface for uploading and managing attachments directly from the product form or dedicated attachment manager.</p>
    </div>

    <p style="text-align: center; margin-top: 40px; color: #666;">
        <strong>Compatible with Odoo 18 Community Edition</strong>
    </p>
</body>
</html>
