/* PDF Preview Modal Styles */

.pdf-preview-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    overflow: hidden;
}

.pdf-preview-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pdf-preview-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    height: 90%;
    max-width: 1200px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.pdf-preview-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.pdf-preview-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
}

.pdf-preview-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pdf-preview-btn {
    background: none;
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s;
}

.pdf-preview-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.pdf-preview-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pdf-preview-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
}

.pdf-preview-close:hover {
    background-color: #e9ecef;
    color: #495057;
}

.pdf-preview-body {
    flex: 1;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.pdf-preview-canvas-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.pdf-preview-canvas {
    max-width: 100%;
    max-height: 100%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pdf-preview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.pdf-preview-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pdf-preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #dc3545;
}

.pdf-preview-error i {
    font-size: 48px;
    margin-bottom: 15px;
}

.pdf-page-info {
    font-size: 14px;
    color: #6c757d;
    margin: 0 10px;
}

.pdf-zoom-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.pdf-zoom-level {
    font-size: 14px;
    color: #495057;
    min-width: 50px;
    text-align: center;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .pdf-preview-content {
        width: 95%;
        height: 95%;
        border-radius: 4px;
    }
    
    .pdf-preview-header {
        padding: 10px 15px;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .pdf-preview-title {
        font-size: 16px;
        width: 100%;
        margin-bottom: 5px;
    }
    
    .pdf-preview-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .pdf-preview-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .pdf-preview-body {
        padding: 10px;
    }
    
    .pdf-page-info {
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .pdf-preview-content {
        width: 100%;
        height: 100%;
        border-radius: 0;
    }
    
    .pdf-preview-header {
        padding: 8px 12px;
    }
    
    .pdf-preview-controls {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .pdf-zoom-controls {
        order: 2;
        width: 100%;
        justify-content: center;
        margin-top: 5px;
    }
}

/* Print styles */
@media print {
    .pdf-preview-modal {
        display: none !important;
    }
}
