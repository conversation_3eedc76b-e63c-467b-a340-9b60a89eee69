/* Product Attachments Styling */

.product-attachments-section {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.product-attachments-section h4 {
    color: #2c3e50;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.attachment-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.attachment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.attachment-icon {
    font-size: 2rem;
    color: #3498db;
    min-width: 60px;
    text-align: center;
}

.attachment-info {
    flex-grow: 1;
}

.attachment-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.attachment-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.attachment-meta {
    color: #6c757d;
    font-size: 0.75rem;
}

.attachment-download-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.attachment-download-btn:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.attachment-download-btn i {
    margin-right: 0.25rem;
}

/* Compact Layout Styles */
.attachment-list-item {
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.attachment-list-item:hover {
    background-color: #f8f9fa;
}

.attachment-list-item:last-child {
    border-bottom: none;
}

/* Tab Layout Styles */
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #3498db;
    color: #3498db;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #3498db;
    color: #3498db;
}

/* File Type Icons */
.fa-file-pdf-o { color: #e74c3c; }
.fa-file-word-o { color: #2980b9; }
.fa-file-excel-o { color: #27ae60; }
.fa-file-powerpoint-o { color: #e67e22; }
.fa-file-image-o { color: #9b59b6; }
.fa-file-video-o { color: #e74c3c; }
.fa-file-audio-o { color: #f39c12; }
.fa-file-archive-o { color: #95a5a6; }
.fa-file-text-o { color: #34495e; }

/* Responsive Design */
@media (max-width: 768px) {
    .attachment-card .card-body {
        flex-direction: column;
        text-align: center;
    }
    
    .attachment-icon {
        margin-bottom: 1rem;
    }
    
    .attachment-download-btn {
        margin-top: 1rem;
        width: 100%;
    }
}

/* Portal Attachments Page */
.portal-attachments .attachment-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.portal-attachments .attachment-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.portal-attachments .attachment-product {
    color: #3498db;
    font-weight: 600;
    text-decoration: none;
}

.portal-attachments .attachment-product:hover {
    text-decoration: underline;
}

/* Loading Animation */
.attachment-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
