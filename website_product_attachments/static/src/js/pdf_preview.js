/* PDF Preview Functionality using PDF.js */

class PDFPreview {
    constructor() {
        this.modal = null;
        this.pdfDoc = null;
        this.currentPage = 1;
        this.totalPages = 0;
        this.scale = 1.0;
        this.canvas = null;
        this.ctx = null;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        const modalHTML = `
            <div id="pdfPreviewModal" class="pdf-preview-modal">
                <div class="pdf-preview-content">
                    <div class="pdf-preview-header">
                        <h4 class="pdf-preview-title" id="pdfPreviewTitle">PDF Preview</h4>
                        <div class="pdf-preview-controls">
                            <button type="button" class="pdf-preview-btn" id="pdfPrevPage" disabled>
                                <i class="fa fa-chevron-left"></i> Previous
                            </button>
                            <span class="pdf-page-info" id="pdfPageInfo">1 / 1</span>
                            <button type="button" class="pdf-preview-btn" id="pdfNextPage" disabled>
                                Next <i class="fa fa-chevron-right"></i>
                            </button>
                            <div class="pdf-zoom-controls">
                                <button type="button" class="pdf-preview-btn" id="pdfZoomOut">
                                    <i class="fa fa-minus"></i>
                                </button>
                                <span class="pdf-zoom-level" id="pdfZoomLevel">100%</span>
                                <button type="button" class="pdf-preview-btn" id="pdfZoomIn">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                            <button type="button" class="pdf-preview-btn" id="pdfDownload">
                                <i class="fa fa-download"></i> Download
                            </button>
                        </div>
                        <button type="button" class="pdf-preview-close" id="pdfPreviewClose">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="pdf-preview-body">
                        <div class="pdf-preview-loading" id="pdfPreviewLoading">
                            <div class="spinner"></div>
                            <p>Loading PDF...</p>
                        </div>
                        <div class="pdf-preview-error" id="pdfPreviewError" style="display: none;">
                            <i class="fa fa-exclamation-triangle"></i>
                            <p>Error loading PDF. Please try downloading instead.</p>
                        </div>
                        <div class="pdf-preview-canvas-container" id="pdfCanvasContainer" style="display: none;">
                            <canvas id="pdfPreviewCanvas" class="pdf-preview-canvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('pdfPreviewModal');
        this.canvas = document.getElementById('pdfPreviewCanvas');
        this.ctx = this.canvas.getContext('2d');
    }
    
    bindEvents() {
        // Close modal events
        document.getElementById('pdfPreviewClose').addEventListener('click', () => this.close());
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.close();
        });
        
        // Navigation events
        document.getElementById('pdfPrevPage').addEventListener('click', () => this.prevPage());
        document.getElementById('pdfNextPage').addEventListener('click', () => this.nextPage());
        
        // Zoom events
        document.getElementById('pdfZoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('pdfZoomOut').addEventListener('click', () => this.zoomOut());
        
        // Download event
        document.getElementById('pdfDownload').addEventListener('click', () => this.download());
        
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (this.modal.classList.contains('show')) {
                switch(e.key) {
                    case 'Escape':
                        this.close();
                        break;
                    case 'ArrowLeft':
                        this.prevPage();
                        break;
                    case 'ArrowRight':
                        this.nextPage();
                        break;
                }
            }
        });
    }
    
    async open(url, filename) {
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        document.getElementById('pdfPreviewTitle').textContent = filename || 'PDF Preview';
        this.showLoading();
        
        try {
            // Use browser's built-in PDF.js if available
            if (typeof pdfjsLib !== 'undefined') {
                this.pdfDoc = await pdfjsLib.getDocument(url).promise;
            } else {
                // Fallback: try to load PDF.js from CDN
                await this.loadPDFJS();
                this.pdfDoc = await pdfjsLib.getDocument(url).promise;
            }
            
            this.totalPages = this.pdfDoc.numPages;
            this.currentPage = 1;
            this.updatePageInfo();
            this.renderPage();
            
        } catch (error) {
            console.error('Error loading PDF:', error);
            this.showError();
        }
    }
    
    async loadPDFJS() {
        return new Promise((resolve, reject) => {
            if (typeof pdfjsLib !== 'undefined') {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
            script.onload = () => {
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
                resolve();
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    async renderPage() {
        if (!this.pdfDoc || this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const page = await this.pdfDoc.getPage(this.currentPage);
            const viewport = page.getViewport({ scale: this.scale });
            
            this.canvas.width = viewport.width;
            this.canvas.height = viewport.height;
            
            const renderContext = {
                canvasContext: this.ctx,
                viewport: viewport
            };
            
            await page.render(renderContext).promise;
            this.showCanvas();
            this.updateControls();
            
        } catch (error) {
            console.error('Error rendering page:', error);
            this.showError();
        } finally {
            this.isLoading = false;
        }
    }
    
    prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.renderPage();
        }
    }
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.renderPage();
        }
    }
    
    zoomIn() {
        this.scale = Math.min(this.scale * 1.2, 3.0);
        this.renderPage();
    }
    
    zoomOut() {
        this.scale = Math.max(this.scale / 1.2, 0.5);
        this.renderPage();
    }
    
    updatePageInfo() {
        document.getElementById('pdfPageInfo').textContent = `${this.currentPage} / ${this.totalPages}`;
    }
    
    updateControls() {
        document.getElementById('pdfPrevPage').disabled = this.currentPage <= 1;
        document.getElementById('pdfNextPage').disabled = this.currentPage >= this.totalPages;
        document.getElementById('pdfZoomLevel').textContent = Math.round(this.scale * 100) + '%';
        this.updatePageInfo();
    }
    
    showLoading() {
        document.getElementById('pdfPreviewLoading').style.display = 'flex';
        document.getElementById('pdfPreviewError').style.display = 'none';
        document.getElementById('pdfCanvasContainer').style.display = 'none';
    }
    
    showError() {
        document.getElementById('pdfPreviewLoading').style.display = 'none';
        document.getElementById('pdfPreviewError').style.display = 'flex';
        document.getElementById('pdfCanvasContainer').style.display = 'none';
    }
    
    showCanvas() {
        document.getElementById('pdfPreviewLoading').style.display = 'none';
        document.getElementById('pdfPreviewError').style.display = 'none';
        document.getElementById('pdfCanvasContainer').style.display = 'flex';
    }
    
    download() {
        // Trigger download of the current PDF
        const downloadBtn = document.getElementById('pdfDownload');
        if (downloadBtn.dataset.downloadUrl) {
            window.open(downloadBtn.dataset.downloadUrl, '_blank');
        }
    }
    
    close() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
        
        // Clean up
        if (this.pdfDoc) {
            this.pdfDoc.destroy();
            this.pdfDoc = null;
        }
        this.currentPage = 1;
        this.totalPages = 0;
        this.scale = 1.0;
    }
}

// Initialize PDF Preview when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.pdfPreview = new PDFPreview();
});

// Global function to open PDF preview
function openPDFPreview(url, filename, downloadUrl) {
    if (window.pdfPreview) {
        // Set download URL for the download button
        document.getElementById('pdfDownload').dataset.downloadUrl = downloadUrl || url;
        window.pdfPreview.open(url, filename);
    }
}
