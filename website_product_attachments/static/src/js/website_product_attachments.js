/* Product Attachments JavaScript */

odoo.define('website_product_attachments.attachments', function (require) {
    'use strict';

    // Check if modules are available before requiring them
    try {
        var publicWidget = require('web.public.widget');
        var core = require('web.core');
    } catch (e) {
        console.log('Required modules not available, skipping widget initialization');
        return {};
    }
    var _t = core._t;

    publicWidget.registry.ProductAttachments = publicWidget.Widget.extend({
        selector: '.product-attachments-section',
        events: {
            'click .attachment-download-btn': '_onDownloadClick',
            'click .attachment-preview-btn': '_onPreviewClick',
        },

        /**
         * Handle download button click
         */
        _onDownloadClick: function (ev) {
            var $btn = $(ev.currentTarget);
            var originalText = $btn.html();
            
            // Show loading state
            $btn.html('<i class="fa fa-spinner fa-spin"></i> ' + _t('Downloading...'));
            $btn.prop('disabled', true);
            
            // Reset button after a short delay
            setTimeout(function () {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 2000);
        },

        /**
         * Handle preview button click
         */
        _onPreviewClick: function (ev) {
            ev.preventDefault();
            var $btn = $(ev.currentTarget);
            var previewUrl = $btn.attr('href');
            
            // Open preview in a new window
            var previewWindow = window.open(
                previewUrl,
                'attachment_preview',
                'width=800,height=600,scrollbars=yes,resizable=yes'
            );
            
            if (!previewWindow) {
                // Fallback if popup is blocked
                window.location.href = previewUrl;
            }
        },
    });

    // Analytics tracking for downloads
    publicWidget.registry.AttachmentAnalytics = publicWidget.Widget.extend({
        selector: '.attachment-download-btn',
        events: {
            'click': '_trackDownload',
        },

        _trackDownload: function (ev) {
            var $link = $(ev.currentTarget);
            var attachmentName = $link.closest('.attachment-card, .attachment-item')
                                     .find('.attachment-title, .attachment-name')
                                     .text().trim();
            
            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'download', {
                    'event_category': 'Product Attachment',
                    'event_label': attachmentName,
                    'value': 1
                });
            }
            
            // Track with Facebook Pixel if available
            if (typeof fbq !== 'undefined') {
                fbq('track', 'ViewContent', {
                    content_type: 'product_attachment',
                    content_name: attachmentName
                });
            }
        },
    });

    // Lazy loading for attachment previews
    publicWidget.registry.AttachmentLazyLoad = publicWidget.Widget.extend({
        selector: '.attachment-preview-thumbnail',
        
        start: function () {
            this._super.apply(this, arguments);
            this._setupLazyLoading();
        },

        _setupLazyLoading: function () {
            var self = this;
            
            // Use Intersection Observer if available
            if ('IntersectionObserver' in window) {
                var observer = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            self._loadPreview($(entry.target));
                            observer.unobserve(entry.target);
                        }
                    });
                });
                
                this.$el.each(function () {
                    observer.observe(this);
                });
            } else {
                // Fallback for older browsers
                this._loadPreview(this.$el);
            }
        },

        _loadPreview: function ($element) {
            var previewUrl = $element.data('preview-url');
            if (previewUrl) {
                $element.attr('src', previewUrl);
            }
        },
    });

    // Search functionality for portal attachments
    publicWidget.registry.AttachmentSearch = publicWidget.Widget.extend({
        selector: '.portal-attachments-search',
        events: {
            'input .search-input': '_onSearchInput',
            'change .search-filter': '_onFilterChange',
        },

        start: function () {
            this._super.apply(this, arguments);
            this.searchTimeout = null;
        },

        _onSearchInput: function (ev) {
            var self = this;
            var query = $(ev.currentTarget).val();
            
            // Debounce search
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(function () {
                self._performSearch(query);
            }, 300);
        },

        _onFilterChange: function (ev) {
            var filter = $(ev.currentTarget).val();
            this._applyFilter(filter);
        },

        _performSearch: function (query) {
            var $items = $('.attachment-item');
            
            if (!query) {
                $items.show();
                return;
            }
            
            query = query.toLowerCase();
            $items.each(function () {
                var $item = $(this);
                var text = $item.text().toLowerCase();
                
                if (text.indexOf(query) !== -1) {
                    $item.show();
                } else {
                    $item.hide();
                }
            });
        },

        _applyFilter: function (filter) {
            var $items = $('.attachment-item');
            
            if (!filter || filter === 'all') {
                $items.show();
                return;
            }
            
            $items.each(function () {
                var $item = $(this);
                var fileType = $item.data('file-type') || '';
                
                if (fileType.indexOf(filter) !== -1) {
                    $item.show();
                } else {
                    $item.hide();
                }
            });
        },
    });

    return {
        ProductAttachments: publicWidget.registry.ProductAttachments,
        AttachmentAnalytics: publicWidget.registry.AttachmentAnalytics,
        AttachmentLazyLoad: publicWidget.registry.AttachmentLazyLoad,
        AttachmentSearch: publicWidget.registry.AttachmentSearch,
    };
});

// Initialize when DOM is ready
$(document).ready(function () {
    'use strict';
    
    // Add smooth scrolling to attachment section
    $('a[href="#attachments"]').on('click', function (e) {
        e.preventDefault();
        var target = $('#attachments');
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // Add tooltips to download buttons
    $('[data-toggle="tooltip"]').tooltip();
    
    // Handle file size display
    $('.file-size').each(function () {
        var $this = $(this);
        var bytes = parseInt($this.data('bytes'));
        if (bytes) {
            $this.text(formatFileSize(bytes));
        }
    });
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        var k = 1024;
        var sizes = ['B', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
});
