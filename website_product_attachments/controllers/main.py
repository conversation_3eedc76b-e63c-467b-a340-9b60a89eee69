# -*- coding: utf-8 -*-

import base64
import logging
from odoo import http, _
from odoo.http import request, Response
from odoo.exceptions import AccessError, MissingError
from odoo.addons.portal.controllers.portal import CustomerPortal

_logger = logging.getLogger(__name__)


class ProductAttachmentController(http.Controller):

    @http.route(['/product/attachment/download/<int:attachment_id>'], 
                type='http', auth='public', website=True)
    def download_product_attachment(self, attachment_id, **kwargs):
        """Download product attachment file"""
        try:
            # Get the attachment record
            attachment = request.env['product.attachment'].sudo().browse(attachment_id)
            
            # Check if attachment exists and is published
            if not attachment.exists():
                return request.not_found()
            
            if not attachment.active or not attachment.website_published:
                return request.not_found()
            
            # Check if the product is published on website
            if not attachment.product_tmpl_id.is_published:
                return request.not_found()
            
            # Increment download counter
            attachment.increment_download_count()
            
            # Prepare file data
            if not attachment.attachment_file:
                return request.not_found()
            
            file_data = base64.b64decode(attachment.attachment_file)
            filename = attachment.filename or attachment.name
            
            # Determine content type
            content_type = attachment.file_type or 'application/octet-stream'
            
            # Create response
            response = Response(
                file_data,
                headers=[
                    ('Content-Type', content_type),
                    ('Content-Disposition', f'attachment; filename="{filename}"'),
                    ('Content-Length', len(file_data)),
                ]
            )
            
            return response
            
        except (AccessError, MissingError):
            return request.not_found()
        except Exception as e:
            _logger.error("Error downloading attachment %s: %s", attachment_id, str(e))
            return request.not_found()

    @http.route(['/product/attachment/preview/<int:attachment_id>'], 
                type='http', auth='public', website=True)
    def preview_product_attachment(self, attachment_id, **kwargs):
        """Preview product attachment file (for images, PDFs, etc.)"""
        try:
            # Get the attachment record
            attachment = request.env['product.attachment'].sudo().browse(attachment_id)
            
            # Check if attachment exists and is published
            if not attachment.exists():
                return request.not_found()
            
            if not attachment.active or not attachment.website_published:
                return request.not_found()
            
            # Check if the product is published on website
            if not attachment.product_tmpl_id.is_published:
                return request.not_found()
            
            # Only allow preview for certain file types
            previewable_types = [
                'image/', 'application/pdf', 'text/', 
                'application/json', 'application/xml'
            ]
            
            if not any(attachment.file_type.startswith(ptype) for ptype in previewable_types):
                # Redirect to download for non-previewable files
                return request.redirect(f'/product/attachment/download/{attachment_id}')
            
            # Prepare file data
            if not attachment.attachment_file:
                return request.not_found()
            
            file_data = base64.b64decode(attachment.attachment_file)
            filename = attachment.filename or attachment.name
            
            # Determine content type
            content_type = attachment.file_type or 'application/octet-stream'
            
            # Create response for preview (inline)
            response = Response(
                file_data,
                headers=[
                    ('Content-Type', content_type),
                    ('Content-Disposition', f'inline; filename="{filename}"'),
                    ('Content-Length', len(file_data)),
                ]
            )
            
            return response
            
        except (AccessError, MissingError):
            return request.not_found()
        except Exception as e:
            _logger.error("Error previewing attachment %s: %s", attachment_id, str(e))
            return request.not_found()


class ProductAttachmentPortal(CustomerPortal):
    """Extend portal to include product attachments in customer portal"""

    def _prepare_home_portal_values(self, counters):
        """Add attachment count to portal home"""
        values = super()._prepare_home_portal_values(counters)
        
        if 'attachment_count' in counters:
            # Count attachments from purchased products
            partner = request.env.user.partner_id
            domain = [
                ('partner_id', '=', partner.id),
                ('state', 'in', ['sale', 'done'])
            ]
            orders = request.env['sale.order'].search(domain)
            product_ids = orders.mapped('order_line.product_id.product_tmpl_id.id')
            
            attachment_count = request.env['product.attachment'].search_count([
                ('product_tmpl_id', 'in', product_ids),
                ('active', '=', True),
                ('website_published', '=', True)
            ])
            values['attachment_count'] = attachment_count
            
        return values

    @http.route(['/my/attachments', '/my/attachments/page/<int:page>'], 
                type='http', auth="user", website=True)
    def portal_my_attachments(self, page=1, date_begin=None, date_end=None, 
                             sortby=None, search=None, search_in='name', **kw):
        """Display customer's product attachments"""
        values = self._prepare_portal_layout_values()
        partner = request.env.user.partner_id
        
        # Get purchased products
        domain = [
            ('partner_id', '=', partner.id),
            ('state', 'in', ['sale', 'done'])
        ]
        orders = request.env['sale.order'].search(domain)
        product_ids = orders.mapped('order_line.product_id.product_tmpl_id.id')
        
        # Search domain for attachments
        attachment_domain = [
            ('product_tmpl_id', 'in', product_ids),
            ('active', '=', True),
            ('website_published', '=', True)
        ]
        
        # Search functionality
        if search and search_in:
            search_domain = []
            if search_in in ('name', 'all'):
                search_domain = ['|', ('name', 'ilike', search)] + search_domain
            if search_in in ('product', 'all'):
                search_domain = ['|', ('product_tmpl_id.name', 'ilike', search)] + search_domain
            if search_in in ('description', 'all'):
                search_domain = ['|', ('description', 'ilike', search)] + search_domain
            attachment_domain += search_domain

        # Sorting options
        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Name'), 'order': 'name'},
            'product': {'label': _('Product'), 'order': 'product_tmpl_id'},
        }
        
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']

        # Search inputs
        searchbar_inputs = {
            'name': {'input': 'name', 'label': _('Search in Name')},
            'product': {'input': 'product', 'label': _('Search in Product')},
            'description': {'input': 'description', 'label': _('Search in Description')},
            'all': {'input': 'all', 'label': _('Search in All')},
        }

        # Pager
        attachment_count = request.env['product.attachment'].search_count(attachment_domain)
        pager = request.website.pager(
            url="/my/attachments",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby},
            total=attachment_count,
            page=page,
            step=self._items_per_page
        )

        # Get attachments
        attachments = request.env['product.attachment'].search(
            attachment_domain, order=order, limit=self._items_per_page, offset=pager['offset']
        )

        values.update({
            'date': date_begin,
            'date_end': date_end,
            'attachments': attachments,
            'page_name': 'attachment',
            'archive_groups': [],
            'default_url': '/my/attachments',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
        })
        return request.render("website_product_attachments.portal_my_attachments", values)
