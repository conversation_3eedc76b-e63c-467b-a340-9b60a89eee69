#!/usr/bin/env python3
"""
Test script to help verify bi_website_hide_price functionality on product detail pages
"""

def display_testing_steps():
    """Display step-by-step testing instructions"""
    print("🔧 TESTING BI_WEBSITE_HIDE_PRICE ON PRODUCT DETAIL PAGES")
    print("="*60)
    
    print("\n📋 STEP 1: VERIFY MODULE INSTALLATION")
    print("   1. Go to Apps menu")
    print("   2. Search for 'bi_website_hide_price'")
    print("   3. Verify it shows as 'Installed'")
    print("   4. If not installed, install it now")
    
    print("\n📋 STEP 2: CONFIGURE A TEST PRODUCT")
    print("   1. Go to Sales > Products > Products")
    print("   2. Find or create a product (e.g., 'Chair Floor Protection')")
    print("   3. Edit the product")
    print("   4. In the Sales tab, find 'Website Price Visibility' section")
    print("   5. Check ✓ 'Website Hide Price'")
    print("   6. Enter custom message: 'Please contact us for pricing'")
    print("   7. Make sure 'Can be Sold' is checked")
    print("   8. Make sure 'Published on Website' is checked")
    print("   9. Save the product")
    
    print("\n📋 STEP 3: TEST PRODUCT LISTING PAGE")
    print("   1. Go to Website > Shop")
    print("   2. Find your test product")
    print("   3. Verify:")
    print("      ✅ Price is hidden")
    print("      ✅ Custom message is displayed")
    print("      ✅ No 'Add to Cart' button")
    
    print("\n📋 STEP 4: TEST PRODUCT DETAIL PAGE")
    print("   1. Click on your test product to open detail page")
    print("   2. URL should be like: /shop/product-name-123")
    print("   3. Verify:")
    print("      ✅ Price is hidden on detail page")
    print("      ✅ Custom message is displayed")
    print("      ✅ 'Add to Cart' is replaced with 'Contact Us' button")
    print("      ✅ No JavaScript errors in browser console")

def display_troubleshooting():
    """Display troubleshooting steps"""
    print("\n🔧 TROUBLESHOOTING:")
    print("="*40)
    
    print("\n❌ IF PRICE STILL SHOWS:")
    print("   1. Check product configuration:")
    print("      - Is 'Website Hide Price' checked?")
    print("      - Is the product published on website?")
    print("   2. Clear browser cache (Ctrl+F5)")
    print("   3. Try incognito/private browsing mode")
    print("   4. Check browser console for errors (F12)")
    
    print("\n❌ IF MODULE NOT WORKING:")
    print("   1. Verify module is installed:")
    print("      - Apps > Search 'bi_website_hide_price'")
    print("   2. Check Odoo logs:")
    print("      - docker-compose logs odoo --tail=20")
    print("   3. Restart Odoo:")
    print("      - docker-compose restart odoo")
    print("   4. Update module:")
    print("      - Apps > bi_website_hide_price > Upgrade")
    
    print("\n❌ IF JAVASCRIPT ERRORS:")
    print("   1. Open browser developer tools (F12)")
    print("   2. Check Console tab for errors")
    print("   3. If errors found, report them for fixing")
    print("   4. Try different browser")

def display_expected_behavior():
    """Display what should happen"""
    print("\n🎯 EXPECTED BEHAVIOR:")
    print("="*40)
    
    print("\n✅ ON PRODUCT LISTING PAGE (/shop):")
    print("   • Products with hidden prices show custom message")
    print("   • Products without hidden prices show normal price")
    print("   • No 'Add to Cart' for price-hidden products")
    
    print("\n✅ ON PRODUCT DETAIL PAGE (/shop/product-name):")
    print("   • Price section shows custom message instead of price")
    print("   • 'Add to Cart' section shows 'Contact Us' button")
    print("   • Button links to contact form (/contactus)")
    print("   • Clean, professional appearance")
    
    print("\n✅ BROWSER CONSOLE:")
    print("   • No JavaScript errors")
    print("   • No template compilation errors")
    print("   • Clean execution")

def display_quick_test():
    """Display quick test procedure"""
    print("\n⚡ QUICK TEST PROCEDURE:")
    print("="*40)
    
    print("\n1. 🔧 SETUP (2 minutes):")
    print("   • Install module if not already installed")
    print("   • Configure one test product with hidden price")
    
    print("\n2. 🧪 TEST (1 minute):")
    print("   • Visit /shop - verify price hidden in list")
    print("   • Click product - verify price hidden on detail page")
    print("   • Check browser console - verify no errors")
    
    print("\n3. ✅ VERIFY (30 seconds):")
    print("   • Custom message displays correctly")
    print("   • Contact button works")
    print("   • No broken layouts")

def main():
    print("🚀 BI_WEBSITE_HIDE_PRICE - PRODUCT DETAIL PAGE TESTING")
    print("="*60)
    print("This guide will help you test the price hiding functionality")
    print("specifically on product detail pages like:")
    print("http://localhost:8069/shop/chair-floor-protection-36")
    
    display_testing_steps()
    display_expected_behavior()
    display_troubleshooting()
    display_quick_test()
    
    print("\n" + "="*60)
    print("🎯 READY TO TEST!")
    print("Follow the steps above to verify your module is working correctly.")
    print("="*60)

if __name__ == "__main__":
    main()
