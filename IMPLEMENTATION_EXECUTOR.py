#!/usr/bin/env python3
"""
BI WEBSITE HIDE PRICE - IMPLEMENTATION EXECUTOR
Step-by-step execution of the comprehensive implementation plan
"""

import os
import subprocess
import time

class CatalogImplementor:
    def __init__(self):
        self.base_path = "bi_website_hide_price"
        self.backup_path = f"{self.base_path}/backups"
        self.current_phase = 1
        
    def create_backup(self, description):
        """Create backup of current state"""
        timestamp = int(time.time())
        backup_name = f"backup_{timestamp}_{description}"
        
        if not os.path.exists(self.backup_path):
            os.makedirs(self.backup_path)
            
        # Backup current template
        if os.path.exists(f"{self.base_path}/views/website_templates.xml"):
            subprocess.run([
                "cp", 
                f"{self.base_path}/views/website_templates.xml",
                f"{self.backup_path}/{backup_name}_templates.xml"
            ])
            print(f"✅ Backup created: {backup_name}")
        
    def phase_1_foundation(self):
        """Phase 1: Foundation Stabilization"""
        print("\n🚀 PHASE 1: FOUNDATION STABILIZATION")
        print("="*50)
        
        # Create backup of current working state
        self.create_backup("phase1_start")
        
        # Create enhanced template with error handling
        enhanced_template = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- BI Website Hide Price - Enhanced Foundation -->
        
        <!-- Phase 1: Core Price and Cart Hiding -->
        <template id="catalog_foundation" inherit_id="website.layout" name="Catalog Foundation">
            <xpath expr="//head" position="inside">
                <style>
                    /* CORE PRICE HIDING - COMPREHENSIVE */
                    .oe_price, .product_price, .oe_currency_value,
                    .price, .amount, .monetary, .currency,
                    [itemprop="price"], [itemprop="offers"],
                    .oe_default_price, .js_price, .o_base_unit_price,
                    .product-price, .sale-price, .list-price,
                    .variant_price, .optional_product_price,
                    span[data-oe-type="monetary"],
                    div[data-oe-type="monetary"],
                    .o_wsale_product_price, .website_sale_price {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                    }

                    /* CORE CART HIDING - COMPREHENSIVE */
                    #add_to_cart, .js_add_cart_variants, .add-to-cart,
                    .o_wsale_product_btn, .js_add_cart, .btn-add-cart,
                    .quantity-selector, .js_quantity, .input-group-quantity,
                    .product-quantity, .qty-selector,
                    input[name="add_qty"], .js_variant_change,
                    .my_cart_quantity, .o_wsale_my_cart {
                        display: none !important;
                    }

                    /* CATALOG FOUNDATION STYLING */
                    .catalog-message {
                        background-color: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                        padding: 15px;
                        margin: 15px 0;
                        text-align: center;
                        color: #495057;
                    }

                    .catalog-contact-btn {
                        background-color: #007bff;
                        color: white;
                        padding: 12px 24px;
                        border: none;
                        border-radius: 5px;
                        text-decoration: none;
                        display: inline-block;
                        margin: 10px 5px;
                        font-weight: bold;
                        transition: background-color 0.3s;
                    }

                    .catalog-contact-btn:hover {
                        background-color: #0056b3;
                        color: white;
                        text-decoration: none;
                    }

                    .catalog-contact-btn.btn-success {
                        background-color: #28a745;
                    }

                    .catalog-contact-btn.btn-success:hover {
                        background-color: #1e7e34;
                    }
                </style>
            </xpath>
        </template>
        
    </data>
</odoo>'''
        
        # Write enhanced template
        with open(f"{self.base_path}/views/website_templates.xml", 'w') as f:
            f.write(enhanced_template)
            
        print("✅ Enhanced foundation template created")
        print("✅ Comprehensive CSS rules added")
        print("✅ Professional styling included")
        
    def phase_2_complete_hiding(self):
        """Phase 2: Complete Price Elimination"""
        print("\n🚀 PHASE 2: COMPLETE PRICE ELIMINATION")
        print("="*50)
        
        self.create_backup("phase2_start")
        
        # Add comprehensive price hiding template
        additional_template = '''
        <!-- Phase 2: Advanced Price Hiding -->
        <template id="catalog_advanced_hiding" inherit_id="website.layout" name="Advanced Price Hiding">
            <xpath expr="//head" position="inside">
                <style>
                    /* ADVANCED PRICE HIDING - ALL POSSIBLE SELECTORS */
                    .price-container, .price-wrapper, .price-box,
                    .product_template_price, .variant_price_extra,
                    .js_product_price, .product_price_total,
                    .price_total, .subtotal, .total-price,
                    .grand-total, .price_extra,
                    *[class*="price"]:not(.catalog-message):not(.catalog-contact-btn),
                    *[class*="cost"]:not(.catalog-message),
                    *[class*="amount"]:not(.catalog-message),
                    *[id*="price"]:not(.catalog-message) {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                        height: 0 !important;
                        overflow: hidden !important;
                    }

                    /* HIDE PRICE-RELATED FORM ELEMENTS */
                    select[name="product_id"], 
                    .js_check_product,
                    .product_variant_price,
                    .o_base_unit_price_wrapper {
                        display: none !important;
                    }

                    /* ENSURE CATALOG ELEMENTS ARE VISIBLE */
                    .catalog-message, .catalog-contact-btn,
                    .catalog-product-info, .catalog-actions {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                    }
                </style>
            </xpath>
        </template>'''
        
        # Read current template and add advanced hiding
        with open(f"{self.base_path}/views/website_templates.xml", 'r') as f:
            current_content = f.read()
        
        # Insert before closing tags
        enhanced_content = current_content.replace(
            '    </data>\n</odoo>',
            additional_template + '\n    </data>\n</odoo>'
        )
        
        with open(f"{self.base_path}/views/website_templates.xml", 'w') as f:
            f.write(enhanced_content)
            
        print("✅ Advanced price hiding rules added")
        print("✅ All possible price selectors covered")
        print("✅ Form elements hidden")
        
    def phase_3_cart_elimination(self):
        """Phase 3: Complete Cart Elimination"""
        print("\n🚀 PHASE 3: COMPLETE CART ELIMINATION")
        print("="*50)
        
        self.create_backup("phase3_start")
        
        # Add cart elimination template
        cart_template = '''
        <!-- Phase 3: Complete Cart Elimination -->
        <template id="catalog_cart_elimination" inherit_id="website.layout" name="Cart Elimination">
            <xpath expr="//head" position="inside">
                <style>
                    /* COMPREHENSIVE CART HIDING */
                    .o_wsale_cart_quantity, .website_sale_cart_quantity,
                    .o_wsale_my_cart_quantity, .cart_quantity,
                    .shopping-cart, .cart-summary, .cart-count,
                    .header-cart, .mini-cart, .cart-widget,
                    .navbar-cart, .top-cart, .cart-dropdown,
                    .cart-icon, .cart-link, .checkout-btn,
                    .proceed-checkout, .continue-shopping {
                        display: none !important;
                    }

                    /* HIDE QUANTITY SELECTORS */
                    .qty, .quantity, .product-quantity,
                    .item-quantity, .quantity-input,
                    .qty-plus, .qty-minus, .qty-controls {
                        display: none !important;
                    }

                    /* PROFESSIONAL PRODUCT CARDS */
                    .o_wsale_product_card {
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                        padding: 20px;
                        margin-bottom: 25px;
                        background: #fff;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                        transition: box-shadow 0.3s;
                    }

                    .o_wsale_product_card:hover {
                        box-shadow: 0 4px 16px rgba(0,0,0,0.12);
                    }
                </style>
            </xpath>
        </template>

        <!-- Header Cart Replacement -->
        <template id="catalog_header_cart" inherit_id="website_sale.header_cart_link" name="Header Cart Replacement">
            <xpath expr="//a[@href='/shop/cart']" position="replace">
                <a href="/contactus" class="nav-link" title="Contact Sales Team">
                    <i class="fa fa-envelope"></i>
                    <span class="d-none d-lg-inline ml-2">Contact Sales</span>
                </a>
            </xpath>
        </template>'''
        
        # Read current template and add cart elimination
        with open(f"{self.base_path}/views/website_templates.xml", 'r') as f:
            current_content = f.read()
        
        # Insert before closing tags
        enhanced_content = current_content.replace(
            '    </data>\n</odoo>',
            cart_template + '\n    </data>\n</odoo>'
        )
        
        with open(f"{self.base_path}/views/website_templates.xml", 'w') as f:
            f.write(enhanced_content)
            
        print("✅ Complete cart elimination added")
        print("✅ Header cart icon replaced with Contact Sales")
        print("✅ Professional product card styling added")
        
    def phase_4_catalog_design(self):
        """Phase 4: Professional Catalog Design"""
        print("\n🚀 PHASE 4: PROFESSIONAL CATALOG DESIGN")
        print("="*50)
        
        self.create_backup("phase4_start")
        
        # Add catalog design templates
        design_template = '''
        <!-- Phase 4: Professional Catalog Design -->
        <template id="catalog_product_messages" inherit_id="website_sale.products_item" name="Catalog Product Messages">
            <xpath expr="//div[hasclass('product_price')]" position="replace">
                <div class="catalog-message">
                    <i class="fa fa-info-circle mr-2"></i>
                    <strong>Contact us for pricing and availability</strong>
                </div>
                <div class="catalog-actions text-center">
                    <a href="/contactus" class="catalog-contact-btn">
                        <i class="fa fa-envelope mr-2"></i>Request Quote
                    </a>
                </div>
            </xpath>
        </template>

        <template id="catalog_product_detail" inherit_id="website_sale.product_price" name="Catalog Product Detail">
            <xpath expr="//span[hasclass('oe_price')]" position="replace">
                <div class="catalog-message">
                    <i class="fa fa-tag mr-2"></i>
                    <strong>Contact our sales team for pricing and detailed specifications</strong>
                </div>
            </xpath>
        </template>

        <template id="catalog_product_cart" inherit_id="website_sale.product_add_to_cart" name="Catalog Product Cart">
            <xpath expr="//div[@id='add_to_cart']" position="replace">
                <div class="catalog-actions text-center mt-4">
                    <h5 class="mb-3">Interested in this product?</h5>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="/contactus" class="catalog-contact-btn btn-block">
                                <i class="fa fa-envelope mr-2"></i>Request Quote
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="tel:+1234567890" class="catalog-contact-btn btn-success btn-block">
                                <i class="fa fa-phone mr-2"></i>Call Sales
                            </a>
                        </div>
                    </div>
                    <p class="text-muted mt-3">
                        <small><i class="fa fa-clock-o mr-1"></i>Our sales team will respond within 24 hours</small>
                    </p>
                </div>
            </xpath>
        </template>'''
        
        # Read current template and add design elements
        with open(f"{self.base_path}/views/website_templates.xml", 'r') as f:
            current_content = f.read()
        
        # Insert before closing tags
        enhanced_content = current_content.replace(
            '    </data>\n</odoo>',
            design_template + '\n    </data>\n</odoo>'
        )
        
        with open(f"{self.base_path}/views/website_templates.xml", 'w') as f:
            f.write(enhanced_content)
            
        print("✅ Professional catalog messages added")
        print("✅ Contact integration implemented")
        print("✅ Product detail enhancements added")
        
    def restart_odoo(self):
        """Restart Odoo to apply changes"""
        print("\n🔄 Restarting Odoo to apply changes...")
        subprocess.run(["docker-compose", "restart", "odoo"])
        time.sleep(20)  # Wait for Odoo to fully restart
        print("✅ Odoo restarted successfully")
        
    def test_implementation(self):
        """Test the implementation"""
        print("\n🧪 TESTING IMPLEMENTATION")
        print("="*40)
        
        test_urls = [
            "http://localhost:8069/shop",
            "http://localhost:8069/shop/chair-floor-protection-36"
        ]
        
        for url in test_urls:
            try:
                import requests
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    print(f"✅ {url} - SUCCESS")
                else:
                    print(f"❌ {url} - Status: {response.status_code}")
            except:
                print(f"⚠️  {url} - Could not test (requests not available)")
                
    def execute_full_implementation(self):
        """Execute the complete implementation plan"""
        print("🚀 STARTING COMPREHENSIVE CATALOG IMPLEMENTATION")
        print("="*60)
        
        try:
            # Execute all phases
            self.phase_1_foundation()
            self.restart_odoo()
            
            self.phase_2_complete_hiding()
            self.restart_odoo()
            
            self.phase_3_cart_elimination()
            self.restart_odoo()
            
            self.phase_4_catalog_design()
            self.restart_odoo()
            
            # Final testing
            self.test_implementation()
            
            print("\n" + "="*60)
            print("🎉 IMPLEMENTATION COMPLETE!")
            print("Your website is now a professional product catalog!")
            print("Visit http://localhost:8069/shop to see the results!")
            print("="*60)
            
        except Exception as e:
            print(f"\n❌ Implementation failed: {e}")
            print("Check the backups in bi_website_hide_price/backups/")

def main():
    implementor = CatalogImplementor()
    implementor.execute_full_implementation()

if __name__ == "__main__":
    main()
