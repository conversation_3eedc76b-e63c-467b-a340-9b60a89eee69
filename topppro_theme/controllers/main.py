from odoo import http
from odoo.http import request
import json


class ToppProController(http.Controller):

    @http.route('/topppro-homepage', type='http', auth='public', website=True)
    def topppro_homepage(self, **kwargs):
        """Custom Topp Pro homepage"""
        return request.render('topppro_theme.topppro_homepage')

    @http.route('/theme/categories/dynamic', type='http', auth='public', website=True)
    def get_dynamic_categories(self, **kwargs):
        """Get product categories for the category grid snippet"""
        try:
            # Get public categories with products
            categories = request.env['product.public.category'].sudo().search([
                ('parent_id', '=', False),  # Only top-level categories
                ('product_tmpl_ids', '!=', False)  # Only categories with products
            ], limit=8)

            category_data = []
            for category in categories:
                # Count products in category
                product_count = len(category.product_tmpl_ids)

                # Get category image or use default
                image_url = '/web/image/product.public.category/%s/image_1920' % category.id if category.image_1920 else '/topppro_theme/static/src/img/categories/default.jpg'

                # Map category to icon
                icon_map = {
                    'mixers': 'fa-sliders',
                    'speakers': 'fa-volume-up',
                    'amplifiers': 'fa-bolt',
                    'wireless': 'fa-wifi',
                    'systems': 'fa-cogs',
                    'processors': 'fa-microchip',
                    'install': 'fa-building',
                    'legacy': 'fa-archive'
                }

                category_slug = category.name.lower().replace(' ', '-')
                icon_class = icon_map.get(category_slug, 'fa-music')

                category_data.append({
                    'id': category.id,
                    'name': category.name,
                    'slug': category_slug,
                    'description': self._get_category_description(category.name),
                    'product_count': product_count,
                    'image_url': image_url,
                    'icon_class': icon_class,
                    'shop_url': '/shop/category/%s-%s' % (category.slug, category.id)
                })

            return request.render('topppro_theme.category_grid_dynamic', {
                'categories': category_data
            })

        except Exception as e:
            # Fallback to static categories if dynamic loading fails
            return request.render('topppro_theme.category_grid_static')

    def _get_category_description(self, category_name):
        """Get appropriate description for category"""
        descriptions = {
            'Mixers': 'Digital and analog mixers for professional audio mixing and live sound applications.',
            'Speakers': 'Active and passive speakers delivering exceptional sound quality for any venue.',
            'Power Amplifiers': 'Professional power amplifiers with advanced protection and high efficiency.',
            'Wireless Systems': 'Wireless microphones and transmitters for freedom of movement and performance.',
            'Systems': 'Complete audio systems including line arrays and event series for large venues.',
            'Modules Processors': 'Digital processors and audio modules for advanced sound processing and control.',
            'Install': 'Installation speakers and amplifiers designed for permanent commercial installations.',
            'Legacy Models': 'Legacy models and discontinued products with continued support and documentation.'
        }
        return descriptions.get(category_name, 'Professional audio equipment for various applications.')
