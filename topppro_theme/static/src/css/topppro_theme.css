/* Topp Pro Audio Theme CSS */

/* Professional TopPro Theme Styling */

/* Color Scheme */
:root {
    --primary-color: #1a1a1a;
    --secondary-color: #ff6b35;
    --accent-color: #f0f0f0;
    --text-color: #333;
    --light-gray: #f8f9fa;
    --dark-gray: #666;
    --border-color: #e0e0e0;
}

/* Global Styles */
body {
    font-family: 'Arial', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styling */
.navbar-brand {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color) !important;
}

.navbar {
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #333 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta {
    background-color: var(--secondary-color);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.hero-cta:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
}

/* Product Categories Section */
.categories-section {
    padding: 4rem 0;
    background-color: var(--light-gray);
}

.category-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: var(--secondary-color);
}

.category-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.category-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.category-description {
    color: var(--dark-gray);
    font-size: 0.95rem;
}

/* Featured Products */
.featured-products {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 3rem;
    color: var(--primary-color);
}

.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: var(--light-gray);
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.product-description {
    color: var(--dark-gray);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
    transform: translateY(-1px);
}

/* Shop Page Styling */
.shop-header {
    background-color: var(--primary-color);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.shop-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.shop-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Product Grid */
.product-grid {
    padding: 3rem 0;
}

.oe_product {
    margin-bottom: 2rem;
}

.oe_product_cart {
    border: 1px solid var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.oe_product_cart:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    border-color: var(--secondary-color);
}

.oe_product_image {
    height: 200px;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Footer */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-section h5 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    margin-top: 2rem;
    text-align: center;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .category-card {
        margin-bottom: 2rem;
    }
}

/* Audio Equipment Specific Styling */
.audio-badge {
    background-color: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.specs-table {
    background-color: var(--light-gray);
    border-radius: 5px;
    padding: 1rem;
    margin: 1rem 0;
}

.specs-table table {
    width: 100%;
    margin: 0;
}

.specs-table td {
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.specs-table td:first-child {
    font-weight: 600;
    color: var(--primary-color);
}

/* ===== CATEGORY GRID SNIPPET STYLES ===== */

.s_category_grid {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.category-card {
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.category-card:hover,
.category-card.hovered {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--secondary-color);
}

.category-image-wrapper {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(45deg, #f1f3f4, #e8eaed);
}

.category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.8), rgba(255, 107, 53, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-icon {
    color: white;
    text-align: center;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.category-card:hover .category-icon {
    transform: translateY(0);
}

.category-content {
    padding: 1.5rem;
    text-align: center;
}

.category-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

.category-card:hover .category-title {
    color: var(--secondary-color);
}

.category-description {
    color: var(--dark-gray);
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    height: 3.2rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.category-stats {
    margin-bottom: 1.25rem;
}

.product-count {
    background: linear-gradient(135deg, var(--secondary-color), #ff8c42);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-category {
    background: linear-gradient(135deg, var(--primary-color), #333);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-category:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--secondary-color), #ff8c42);
    transition: left 0.3s ease;
    z-index: 0;
}

.btn-category:hover:before {
    left: 0;
}

.btn-category:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
}

.btn-category span,
.btn-category i {
    position: relative;
    z-index: 1;
}

/* Category Grid Responsive Design */
@media (max-width: 768px) {
    .category-image-wrapper {
        height: 150px;
    }

    .category-content {
        padding: 1rem;
    }

    .category-title {
        font-size: 1.2rem;
    }

    .category-description {
        font-size: 0.85rem;
        height: auto;
        -webkit-line-clamp: 3;
    }

    .btn-category {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .s_category_grid .container {
        padding: 0 15px;
    }

    .category-card {
        margin-bottom: 1.5rem;
    }

    .category-image-wrapper {
        height: 120px;
    }

    .category-content {
        padding: 0.75rem;
    }

    .category-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .category-description {
        font-size: 0.8rem;
        margin-bottom: 0.75rem;
    }

    .btn-category {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}
