/* Topp Pro Audio Theme JavaScript */

odoo.define('topppro_theme.theme', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var core = require('web.core');

    // Smooth scrolling for navigation links
    publicWidget.registry.SmoothScroll = publicWidget.Widget.extend({
        selector: 'a[href^="#"]',
        events: {
            'click': '_onSmoothScroll',
        },

        _onSmoothScroll: function (ev) {
            ev.preventDefault();
            var target = $(ev.currentTarget.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800);
            }
        },
    });

    // Product card hover effects
    publicWidget.registry.ProductCardEffects = publicWidget.Widget.extend({
        selector: '.product-card, .oe_product_cart',
        events: {
            'mouseenter': '_onMouseEnter',
            'mouseleave': '_onMouseLeave',
        },

        _onMouseEnter: function (ev) {
            $(ev.currentTarget).addClass('shadow-lg');
        },

        _onMouseLeave: function (ev) {
            $(ev.currentTarget).removeClass('shadow-lg');
        },
    });

    // Category card animations
    publicWidget.registry.CategoryAnimations = publicWidget.Widget.extend({
        selector: '.category-card',
        
        start: function () {
            this._super.apply(this, arguments);
            this._setupIntersectionObserver();
        },

        _setupIntersectionObserver: function () {
            if ('IntersectionObserver' in window) {
                var observer = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            $(entry.target).addClass('animate__animated animate__fadeInUp');
                        }
                    });
                });

                this.$el.each(function () {
                    observer.observe(this);
                });
            }
        },
    });

    // Featured products carousel
    publicWidget.registry.FeaturedProductsCarousel = publicWidget.Widget.extend({
        selector: '.featured-products-carousel',
        
        start: function () {
            this._super.apply(this, arguments);
            this._initCarousel();
        },

        _initCarousel: function () {
            this.$el.find('.carousel').carousel({
                interval: 5000,
                pause: 'hover'
            });
        },
    });

    // Search functionality enhancement
    publicWidget.registry.EnhancedSearch = publicWidget.Widget.extend({
        selector: '.search-form',
        events: {
            'input .search-input': '_onSearchInput',
            'submit': '_onSearchSubmit',
        },

        _onSearchInput: function (ev) {
            var query = $(ev.currentTarget).val();
            if (query.length > 2) {
                this._performLiveSearch(query);
            }
        },

        _onSearchSubmit: function (ev) {
            ev.preventDefault();
            var query = this.$('.search-input').val();
            if (query) {
                window.location.href = '/shop?search=' + encodeURIComponent(query);
            }
        },

        _performLiveSearch: function (query) {
            // Implement live search functionality
            console.log('Searching for:', query);
        },
    });

    // Mobile menu toggle
    publicWidget.registry.MobileMenu = publicWidget.Widget.extend({
        selector: '.navbar-toggler',
        events: {
            'click': '_onToggleMenu',
        },

        _onToggleMenu: function (ev) {
            var $navbar = $('.navbar-collapse');
            $navbar.toggleClass('show');
        },
    });

    // Product image zoom
    publicWidget.registry.ProductImageZoom = publicWidget.Widget.extend({
        selector: '.product-image-container',
        events: {
            'mouseenter img': '_onImageHover',
            'mouseleave img': '_onImageLeave',
            'mousemove img': '_onImageMove',
        },

        _onImageHover: function (ev) {
            $(ev.currentTarget).css('transform', 'scale(1.1)');
        },

        _onImageLeave: function (ev) {
            $(ev.currentTarget).css('transform', 'scale(1)');
        },

        _onImageMove: function (ev) {
            // Add image zoom on mouse move
            var $img = $(ev.currentTarget);
            var offset = $img.offset();
            var x = (ev.pageX - offset.left) / $img.width() * 100;
            var y = (ev.pageY - offset.top) / $img.height() * 100;
            $img.css('transform-origin', x + '% ' + y + '%');
        },
    });

    // Lazy loading for images
    publicWidget.registry.LazyLoading = publicWidget.Widget.extend({
        selector: 'img[data-src]',
        
        start: function () {
            this._super.apply(this, arguments);
            this._setupLazyLoading();
        },

        _setupLazyLoading: function () {
            if ('IntersectionObserver' in window) {
                var observer = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            var img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            observer.unobserve(img);
                        }
                    });
                });

                this.$el.each(function () {
                    observer.observe(this);
                });
            } else {
                // Fallback for older browsers
                this.$el.each(function () {
                    this.src = this.dataset.src;
                });
            }
        },
    });

    return {
        SmoothScroll: publicWidget.registry.SmoothScroll,
        ProductCardEffects: publicWidget.registry.ProductCardEffects,
        CategoryAnimations: publicWidget.registry.CategoryAnimations,
        FeaturedProductsCarousel: publicWidget.registry.FeaturedProductsCarousel,
        EnhancedSearch: publicWidget.registry.EnhancedSearch,
        MobileMenu: publicWidget.registry.MobileMenu,
        ProductImageZoom: publicWidget.registry.ProductImageZoom,
        LazyLoading: publicWidget.registry.LazyLoading,
    };
});

// Initialize when DOM is ready
$(document).ready(function () {
    'use strict';
    
    // Add loading animation
    $('.page-loading').fadeOut(500);
    
    // Smooth scroll to top button
    $(window).scroll(function () {
        if ($(this).scrollTop() > 100) {
            $('.scroll-to-top').fadeIn();
        } else {
            $('.scroll-to-top').fadeOut();
        }
    });
    
    $('.scroll-to-top').click(function () {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });
    
    // Product filter animations
    $('.filter-option').click(function () {
        $('.filter-option').removeClass('active');
        $(this).addClass('active');
        
        var filter = $(this).data('filter');
        $('.product-item').hide().filter(filter).fadeIn(300);
    });
    
    // Newsletter subscription
    $('.newsletter-form').submit(function (e) {
        e.preventDefault();
        var email = $(this).find('input[type="email"]').val();
        if (email) {
            // Handle newsletter subscription
            console.log('Newsletter subscription:', email);
            $(this).find('.btn').text('Subscribed!').prop('disabled', true);
        }
    });
});
